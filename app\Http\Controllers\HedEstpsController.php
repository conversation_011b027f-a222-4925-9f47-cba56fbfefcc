<?php

namespace App\Http\Controllers;

use App\Models\hed_estps;
use Illuminate\Http\Request;

class HedEstpsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(hed_estps $hed_estps)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(hed_estps $hed_estps)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, hed_estps $hed_estps)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(hed_estps $hed_estps)
    {
        //
    }
}
