<?php $__env->startSection('title'); ?>
إدارة النسخ الاحتياطي
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<style>
.backup-card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
}

.backup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.settings-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.backup-log-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.backup-log-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.status-badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-progress {
    background: #fff3cd;
    color: #856404;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

.backup-stats {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.table-selector {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #007bff;
}

input:checked + .slider:before {
    transform: translateX(26px);
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">النظام</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ النسخ الاحتياطي</span>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- إحصائيات النسخ الاحتياطي -->
    <div class="col-lg-3 col-md-6">
        <div class="backup-stats">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0"><?php echo e($backupLogs->total()); ?></h3>
                    <p class="mb-0">إجمالي النسخ</p>
                </div>
                <i class="fa fa-database fa-2x opacity-50"></i>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><?php echo e($backupLogs->where('status', 'success')->count()); ?></h3>
                        <p class="mb-0">ناجحة</p>
                    </div>
                    <i class="fa fa-check-circle fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><?php echo e($backupLogs->where('status', 'failed')->count()); ?></h3>
                        <p class="mb-0">فاشلة</p>
                    </div>
                    <i class="fa fa-times-circle fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><?php echo e($backupLogs->where('status', 'in_progress')->count()); ?></h3>
                        <p class="mb-0">قيد التنفيذ</p>
                    </div>
                    <i class="fa fa-spinner fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- إعدادات النسخ الاحتياطي -->
    <div class="col-lg-4">
        <div class="backup-card">
            <div class="backup-header">
                <h5 class="mb-0">
                    <i class="fa fa-cog me-2"></i>
                    إعدادات النسخ الاحتياطي
                </h5>
            </div>

            <div class="card-body">
                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fa fa-check me-2"></i>
                        <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form action="<?php echo e(route('backup.settings.update')); ?>" method="POST">
                    <?php echo csrf_field(); ?>

                    <div class="settings-section">
                        <h6 class="text-primary mb-3">الإعدادات العامة</h6>

                        <div class="form-group mb-3">
                            <label class="form-label d-flex justify-content-between align-items-center">
                                تفعيل النسخ التلقائي
                                <label class="switch">
                                    <input type="checkbox" name="auto_backup_enabled" value="1"
                                           <?php echo e($settings->auto_backup_enabled ? 'checked' : ''); ?>>
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">مسار النسخ الاحتياطي</label>
                            <input type="text" name="backup_path" class="form-control"
                                   value="<?php echo e($settings->backup_path); ?>" required>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">رابط النسخ الخارجي (اختياري)</label>
                            <div class="input-group">
                                <input type="url" name="backup_url" class="form-control"
                                       value="<?php echo e($settings->backup_url); ?>" placeholder="https://example.com/backup">
                                <button type="button" class="btn btn-outline-secondary" id="testConnectionBtn">
                                    اختبار
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h6 class="text-primary mb-3">جدولة النسخ</h6>

                        <div class="form-group mb-3">
                            <label class="form-label d-flex justify-content-between align-items-center">
                                نسخ يومي
                                <label class="switch">
                                    <input type="checkbox" name="daily_backup" value="1"
                                           <?php echo e($settings->daily_backup ? 'checked' : ''); ?>>
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label d-flex justify-content-between align-items-center">
                                نسخ أسبوعي
                                <label class="switch">
                                    <input type="checkbox" name="weekly_backup" value="1"
                                           <?php echo e($settings->weekly_backup ? 'checked' : ''); ?>>
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label d-flex justify-content-between align-items-center">
                                نسخ شهري
                                <label class="switch">
                                    <input type="checkbox" name="monthly_backup" value="1"
                                           <?php echo e($settings->monthly_backup ? 'checked' : ''); ?>>
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">وقت النسخ الاحتياطي</label>
                            <input type="time" name="backup_time" class="form-control"
                                   value="<?php echo e($settings->backup_time); ?>" required>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h6 class="text-primary mb-3">إعدادات متقدمة</h6>

                        <div class="form-group mb-3">
                            <label class="form-label">الاحتفاظ بالنسخ لمدة (أيام)</label>
                            <input type="number" name="keep_backups_days" class="form-control"
                                   value="<?php echo e($settings->keep_backups_days); ?>" min="1" required>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label d-flex justify-content-between align-items-center">
                                ضغط النسخ الاحتياطي
                                <label class="switch">
                                    <input type="checkbox" name="compress_backups" value="1"
                                           <?php echo e($settings->compress_backups ? 'checked' : ''); ?>>
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">الجداول المراد نسخها</label>
                            <div class="table-selector">
                                <?php $__currentLoopData = $availableTables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="form-check">
                                        <input type="checkbox" name="backup_tables[]" value="<?php echo e($table); ?>"
                                               class="form-check-input" id="table_<?php echo e($table); ?>"
                                               <?php echo e(in_array($table, $settings->backup_tables ?? []) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="table_<?php echo e($table); ?>">
                                            <?php echo e($name); ?> (<?php echo e($table); ?>)
                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save me-2"></i>
                            حفظ الإعدادات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- إنشاء نسخة احتياطية يدوية -->
    <div class="col-lg-8">
        <div class="backup-card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fa fa-plus me-2"></i>
                    إنشاء نسخة احتياطية يدوية
                </h5>
            </div>

            <div class="card-body">
                <form id="manualBackupForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">نوع النسخة</label>
                                <select name="backup_type" class="form-control">
                                    <option value="manual">يدوي</option>
                                    <option value="emergency">طوارئ</option>
                                    <option value="before_update">قبل التحديث</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">الجداول المحددة</label>
                                <select name="tables[]" class="form-control" multiple size="6">
                                    <?php $__currentLoopData = $availableTables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($table); ?>"><?php echo e($name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <small class="text-muted">
                                    <i class="fa fa-info-circle text-info"></i>
                                    <strong>افتراضي:</strong> نسخ جميع الجداول (اتركه فارغاً)
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-success">
                            <i class="fa fa-play me-2"></i>
                            بدء النسخ الاحتياطي
                        </button>
                        <button type="button" class="btn btn-warning" id="cleanOldBackupsBtn">
                            <i class="fa fa-trash me-2"></i>
                            حذف النسخ القديمة
                        </button>
                    </div>
                </form>

                <!-- شريط التقدم -->
                <div id="backupProgress" style="display: none;" class="mt-3">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="text-center mt-2">
                        <small id="progressText">جاري إنشاء النسخة الاحتياطية...</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل النسخ الاحتياطي -->
        <div class="backup-card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fa fa-history me-2"></i>
                    سجل النسخ الاحتياطي
                </h5>
            </div>

            <div class="card-body">
                <?php if($backupLogs->count() > 0): ?>
                    <?php $__currentLoopData = $backupLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="backup-log-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <h6 class="mb-0 me-2"><?php echo e($log->file_name); ?></h6>
                                        <span class="status-badge status-<?php echo e($log->status); ?>">
                                            <?php if($log->status == 'success'): ?>
                                                <i class="fa fa-check me-1"></i>نجح
                                            <?php elseif($log->status == 'failed'): ?>
                                                <i class="fa fa-times me-1"></i>فشل
                                            <?php else: ?>
                                                <i class="fa fa-spinner fa-spin me-1"></i>قيد التنفيذ
                                            <?php endif; ?>
                                        </span>
                                    </div>

                                    <div class="row text-muted small">
                                        <div class="col-md-3">
                                            <i class="fa fa-calendar me-1"></i>
                                            <?php echo e($log->created_at->format('Y-m-d H:i')); ?>

                                        </div>
                                        <div class="col-md-3">
                                            <i class="fa fa-tag me-1"></i>
                                            <?php echo e($log->backup_type); ?>

                                        </div>
                                        <?php if($log->file_size): ?>
                                            <div class="col-md-3">
                                                <i class="fa fa-hdd me-1"></i>
                                                <?php echo e($log->formatted_file_size); ?>

                                            </div>
                                        <?php endif; ?>
                                        <?php if($log->duration): ?>
                                            <div class="col-md-3">
                                                <i class="fa fa-clock me-1"></i>
                                                <?php echo e($log->formatted_duration); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <?php if($log->error_message): ?>
                                        <div class="alert alert-danger mt-2 mb-0">
                                            <small><i class="fa fa-exclamation-triangle me-1"></i><?php echo e($log->error_message); ?></small>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="backup-actions">
                                    <?php if($log->status == 'success'): ?>
                                        <a href="<?php echo e(route('backup.download', $log->id)); ?>"
                                           class="btn btn-sm btn-outline-primary" title="تحميل">
                                            <i class="fa fa-download"></i>
                                        </a>
                                    <?php endif; ?>
                                    <button class="btn btn-sm btn-outline-danger delete-backup-btn"
                                            data-id="<?php echo e($log->id); ?>" title="حذف">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <!-- الترقيم -->
                    <div class="d-flex justify-content-center mt-3">
                        <?php echo e($backupLogs->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fa fa-database fa-3x text-muted mb-3"></i>
                        <h5>لا توجد نسخ احتياطية</h5>
                        <p class="text-muted">لم يتم إنشاء أي نسخة احتياطية حتى الآن</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء نسخة احتياطية يدوية
    document.getElementById('manualBackupForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const progressDiv = document.getElementById('backupProgress');
        const progressBar = progressDiv.querySelector('.progress-bar');
        const progressText = document.getElementById('progressText');

        // إظهار شريط التقدم
        progressDiv.style.display = 'block';
        progressBar.style.width = '10%';

        try {
            const response = await fetch('<?php echo e(route("backup.create")); ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                progressBar.style.width = '50%';
                progressText.textContent = 'جاري إنشاء النسخة الاحتياطية...';

                // مراقبة حالة النسخة الاحتياطية
                const backupId = data.backup_id;
                const checkStatus = setInterval(async () => {
                    try {
                        const statusResponse = await fetch(`/backup/status/${backupId}`);
                        const statusData = await statusResponse.json();

                        if (statusData.status === 'success') {
                            clearInterval(checkStatus);
                            progressBar.style.width = '100%';
                            progressBar.classList.remove('progress-bar-animated');
                            progressBar.classList.add('bg-success');
                            progressText.textContent = 'تم إنشاء النسخة الاحتياطية بنجاح!';

                            setTimeout(() => {
                                location.reload();
                            }, 2000);
                        } else if (statusData.status === 'failed') {
                            clearInterval(checkStatus);
                            progressBar.classList.remove('progress-bar-animated');
                            progressBar.classList.add('bg-danger');
                            progressText.textContent = 'فشل في إنشاء النسخة الاحتياطية: ' + statusData.error_message;
                        } else {
                            progressBar.style.width = '75%';
                        }
                    } catch (error) {
                        console.error('خطأ في مراقبة حالة النسخة الاحتياطية:', error);
                    }
                }, 2000);

            } else {
                throw new Error(data.message);
            }

        } catch (error) {
            progressBar.classList.remove('progress-bar-animated');
            progressBar.classList.add('bg-danger');
            progressText.textContent = 'خطأ: ' + error.message;
        }
    });

    // اختبار الاتصال
    document.getElementById('testConnectionBtn').addEventListener('click', async function() {
        const urlInput = document.querySelector('input[name="backup_url"]');
        const url = urlInput.value;

        if (!url) {
            alert('يرجى إدخال رابط أولاً');
            return;
        }

        this.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
        this.disabled = true;

        try {
            const response = await fetch('<?php echo e(route("backup.test-connection")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ backup_url: url })
            });

            const data = await response.json();

            if (data.success) {
                alert('تم الاتصال بنجاح!');
            } else {
                alert('فشل الاتصال: ' + data.message);
            }

        } catch (error) {
            alert('خطأ في الاتصال: ' + error.message);
        } finally {
            this.innerHTML = 'اختبار';
            this.disabled = false;
        }
    });

    // حذف النسخ القديمة
    document.getElementById('cleanOldBackupsBtn').addEventListener('click', async function() {
        if (!confirm('هل أنت متأكد من حذف النسخ الاحتياطية القديمة؟')) {
            return;
        }

        this.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>جاري الحذف...';
        this.disabled = true;

        try {
            const response = await fetch('<?php echo e(route("backup.clean-old")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('فشل في حذف النسخ القديمة');
            }

        } catch (error) {
            alert('خطأ: ' + error.message);
        } finally {
            this.innerHTML = '<i class="fa fa-trash me-2"></i>حذف النسخ القديمة';
            this.disabled = false;
        }
    });

    // حذف نسخة احتياطية محددة
    document.querySelectorAll('.delete-backup-btn').forEach(btn => {
        btn.addEventListener('click', async function() {
            if (!confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
                return;
            }

            const id = this.dataset.id;

            try {
                const response = await fetch(`/backup/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    location.reload();
                } else {
                    alert('فشل في حذف النسخة الاحتياطية');
                }

            } catch (error) {
                alert('خطأ: ' + error.message);
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nc\resources\views/backup/index.blade.php ENDPATH**/ ?>