<?php

/**
 * سكريبت إصلاح المشاكل الشائعة لنظام الإشعارات والنسخ الاحتياطي
 * تشغيل هذا الملف: php fix_common_issues.php
 */

echo "🔧 بدء إصلاح المشاكل الشائعة...\n\n";

// 1. مسح cache Laravel
echo "1️⃣ مسح cache Laravel...\n";
exec('php artisan cache:clear', $output1, $return1);
exec('php artisan config:clear', $output2, $return2);
exec('php artisan route:clear', $output3, $return3);
exec('php artisan view:clear', $output4, $return4);

if ($return1 === 0 && $return2 === 0 && $return3 === 0 && $return4 === 0) {
    echo "✅ تم مسح cache بنجاح\n\n";
} else {
    echo "❌ فشل في مسح cache\n\n";
}

// 2. تحديث autoloader
echo "2️⃣ تحديث autoloader...\n";
exec('composer dump-autoload', $output5, $return5);

if ($return5 === 0) {
    echo "✅ تم تحديث autoloader بنجاح\n\n";
} else {
    echo "❌ فشل في تحديث autoloader\n\n";
}

// 3. التحقق من وجود الملفات المطلوبة
echo "3️⃣ التحقق من وجود الملفات المطلوبة...\n";

$requiredFiles = [
    'app/Models/Notification.php',
    'app/Models/NotificationSetting.php',
    'app/Models/BackupSetting.php',
    'app/Models/BackupLog.php',
    'app/Http/Controllers/NotificationController.php',
    'app/Http/Controllers/BackupController.php',
    'app/Observers/ClientObserver.php',
    'app/Console/Commands/CreateBackupCommand.php',
    'resources/views/notifications/index.blade.php',
    'resources/views/notifications/settings.blade.php',
    'resources/views/backup/index.blade.php',
    'public/assets/js/notifications.js'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        $missingFiles[] = $file;
    }
}

if (empty($missingFiles)) {
    echo "✅ جميع الملفات المطلوبة موجودة\n\n";
} else {
    echo "❌ الملفات المفقودة:\n";
    foreach ($missingFiles as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

// 4. التحقق من المجلدات المطلوبة
echo "4️⃣ التحقق من المجلدات المطلوبة...\n";

$requiredDirs = [
    'storage/backups',
    'storage/app/backups',
    'storage/app/temp',
    'public/assets/sounds'
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "📁 تم إنشاء مجلد: $dir\n";
    } else {
        echo "✅ مجلد موجود: $dir\n";
    }
}
echo "\n";

// 5. التحقق من صلاحيات المجلدات
echo "5️⃣ التحقق من صلاحيات المجلدات...\n";

foreach ($requiredDirs as $dir) {
    if (is_writable($dir)) {
        echo "✅ مجلد قابل للكتابة: $dir\n";
    } else {
        chmod($dir, 0755);
        echo "🔧 تم تعديل صلاحيات: $dir\n";
    }
}
echo "\n";

// 6. التحقق من قاعدة البيانات
echo "6️⃣ التحقق من قاعدة البيانات...\n";

try {
    // محاولة الاتصال بقاعدة البيانات
    $pdo = new PDO(
        'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'],
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD']
    );
    
    // التحقق من وجود الجداول المطلوبة
    $requiredTables = [
        'notifications',
        'notification_settings',
        'backup_settings',
        'backup_logs'
    ];
    
    $existingTables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existingTables[] = $row[0];
    }
    
    $missingTables = array_diff($requiredTables, $existingTables);
    
    if (empty($missingTables)) {
        echo "✅ جميع الجداول المطلوبة موجودة\n";
    } else {
        echo "❌ الجداول المفقودة:\n";
        foreach ($missingTables as $table) {
            echo "   - $table\n";
        }
        echo "💡 تشغيل: php artisan migrate\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
}
echo "\n";

// 7. التحقق من المسارات
echo "7️⃣ التحقق من المسارات...\n";
exec('php artisan route:list --name=notifications', $routeOutput, $routeReturn);

if ($routeReturn === 0 && !empty($routeOutput)) {
    echo "✅ مسارات الإشعارات مسجلة بنجاح\n";
} else {
    echo "❌ مشكلة في مسارات الإشعارات\n";
    echo "💡 تحقق من ملف routes/web.php\n";
}
echo "\n";

// 8. إنشاء ملف اختبار
echo "8️⃣ إنشاء ملف اختبار...\n";

$testContent = '<?php
// ملف اختبار للتأكد من عمل النظام
echo "نظام الإشعارات والنسخ الاحتياطي يعمل بشكل صحيح!";
';

file_put_contents('test_system.php', $testContent);
echo "✅ تم إنشاء ملف الاختبار: test_system.php\n\n";

// 9. عرض الخطوات التالية
echo "📋 الخطوات التالية:\n";
echo "1. تشغيل: php artisan migrate (إذا لم تكن الجداول موجودة)\n";
echo "2. تشغيل: php test_system.php (للتأكد من عمل PHP)\n";
echo "3. زيارة: /notifications (لاختبار الإشعارات)\n";
echo "4. زيارة: /backup (لاختبار النسخ الاحتياطي)\n";
echo "5. إضافة ملفات الأصوات إلى public/assets/sounds/\n\n";

echo "🎉 انتهى إصلاح المشاكل الشائعة!\n";
echo "📞 في حالة استمرار المشاكل، تحقق من:\n";
echo "   - storage/logs/laravel.log\n";
echo "   - console المتصفح للأخطاء JavaScript\n";
echo "   - إعدادات قاعدة البيانات في .env\n";

?>
