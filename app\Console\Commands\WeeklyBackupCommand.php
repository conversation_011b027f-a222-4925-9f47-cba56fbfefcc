<?php

namespace App\Console\Commands;

use App\Models\BackupSetting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class WeeklyBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'backup:weekly';

    /**
     * The console command description.
     */
    protected $description = 'إنشاء نسخة احتياطية أسبوعية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $settings = BackupSetting::getSettings();

        if (!$settings->auto_backup_enabled || !$settings->weekly_backup) {
            $this->info('النسخ الاحتياطي الأسبوعي معطل');
            return 0;
        }

        $this->info('بدء النسخ الاحتياطي الأسبوعي...');

        return Artisan::call('backup:create', [
            '--type' => 'weekly'
        ]);
    }
}
