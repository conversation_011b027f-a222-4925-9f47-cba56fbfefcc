<?php

namespace App\Models;

use \Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class clientschallenges extends Model
{
    use HasFactory;

    protected $table = "clientschallenges";
    protected $fillable =
        [
            'tahaddename',
            'startdate',
            'timeenddate',
            'clients_id',
            'tahadde_id',
            'startdateofcustomerchallenges',
            'endtdateofcustomerchallenges',


        ];
    public $timestamps = true;


    public function clientschallengesss()
    {
        return $this->belongsTo('App\Models\challenges','tahadde_id');
    }

    public function clientschallengescliennt()
    {
        return $this->belongsTo('App\Models\clients','clients_id');
    }

}
