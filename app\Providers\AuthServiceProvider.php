<?php

namespace App\Providers;


use App\Models\User;
use App\Policies\roloPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    protected $policies=[
    User::class=>roloPolicy::class,
];
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
       $this->registerPolicies();

        //
    }
}
