<?php

/**
 * اختبار شامل لنظام الإشعارات والنسخ الاحتياطي
 * تشغيل: php test_complete_system.php
 */

echo "🧪 اختبار شامل لنظام الإشعارات والنسخ الاحتياطي\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// تحميل Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل Laravel: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. اختبار قاعدة البيانات
echo "1️⃣ اختبار قاعدة البيانات...\n";
try {
    DB::connection()->getPdo();
    echo "✅ الاتصال بقاعدة البيانات ناجح\n";
    
    // فحص الجداول
    $tables = ['notifications', 'notification_settings', 'backup_settings', 'backup_logs'];
    foreach ($tables as $table) {
        try {
            $count = DB::table($table)->count();
            echo "✅ جدول {$table}: {$count} سجل\n";
        } catch (Exception $e) {
            echo "❌ جدول {$table}: غير موجود\n";
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
}
echo "\n";

// 2. اختبار النماذج
echo "2️⃣ اختبار النماذج...\n";
$models = [
    'App\Models\Notification',
    'App\Models\NotificationSetting', 
    'App\Models\BackupSetting',
    'App\Models\BackupLog'
];

foreach ($models as $model) {
    try {
        $instance = new $model();
        echo "✅ نموذج {$model}: يعمل\n";
    } catch (Exception $e) {
        echo "❌ نموذج {$model}: خطأ - " . $e->getMessage() . "\n";
    }
}
echo "\n";

// 3. اختبار المسارات
echo "3️⃣ اختبار المسارات...\n";
try {
    $routes = [
        'notifications.index',
        'notifications.settings',
        'backup.index'
    ];
    
    foreach ($routes as $routeName) {
        try {
            $url = route($routeName);
            echo "✅ مسار {$routeName}: {$url}\n";
        } catch (Exception $e) {
            echo "❌ مسار {$routeName}: غير موجود\n";
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في المسارات: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار الملفات المطلوبة
echo "4️⃣ اختبار الملفات المطلوبة...\n";
$files = [
    'app/Http/Controllers/NotificationController.php',
    'app/Http/Controllers/BackupController.php',
    'app/Observers/ClientObserver.php',
    'public/assets/js/notifications.js',
    'public/assets/css/notifications.css',
    'resources/views/notifications/index.blade.php',
    'resources/views/notifications/settings.blade.php',
    'resources/views/backup/index.blade.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ ملف {$file}: موجود\n";
    } else {
        echo "❌ ملف {$file}: مفقود\n";
    }
}
echo "\n";

// 5. اختبار المجلدات
echo "5️⃣ اختبار المجلدات...\n";
$directories = [
    'storage/backups',
    'storage/app/backups', 
    'public/assets/sounds',
    'public/assets/css'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        echo "✅ مجلد {$dir}: موجود ({$writable})\n";
    } else {
        echo "❌ مجلد {$dir}: مفقود\n";
    }
}
echo "\n";

// 6. اختبار الأصوات
echo "6️⃣ اختبار ملفات الأصوات...\n";
$sounds = [
    'notification.mp3',
    'bell.mp3', 
    'chime.mp3',
    'ding.mp3',
    'pop.mp3',
    'swoosh.mp3'
];

foreach ($sounds as $sound) {
    $path = "public/assets/sounds/{$sound}";
    if (file_exists($path)) {
        $size = filesize($path);
        echo "✅ صوت {$sound}: موجود ({$size} بايت)\n";
    } else {
        echo "⚠️  صوت {$sound}: مفقود (سيتم استخدام الافتراضي)\n";
    }
}
echo "\n";

// 7. اختبار إنشاء إشعار
echo "7️⃣ اختبار إنشاء إشعار...\n";
try {
    $user = App\Models\User::first();
    if ($user) {
        $notification = App\Models\Notification::createNotification([
            'type' => 'system_test',
            'title' => 'اختبار النظام',
            'message' => 'تم اختبار نظام الإشعارات بنجاح في ' . now()->format('Y-m-d H:i:s'),
            'icon' => 'fa-check-circle',
            'color' => 'success',
            'user_id' => $user->id
        ]);
        echo "✅ تم إنشاء إشعار اختبار (ID: {$notification->id})\n";
    } else {
        echo "⚠️  لا يوجد مستخدمين لاختبار الإشعار\n";
    }
} catch (Exception $e) {
    echo "❌ فشل في إنشاء إشعار: " . $e->getMessage() . "\n";
}
echo "\n";

// 8. اختبار إعدادات النسخ الاحتياطي
echo "8️⃣ اختبار إعدادات النسخ الاحتياطي...\n";
try {
    $settings = App\Models\BackupSetting::getSettings();
    echo "✅ إعدادات النسخ الاحتياطي:\n";
    echo "   - النسخ التلقائي: " . ($settings->auto_backup_enabled ? 'مُفعل' : 'مُعطل') . "\n";
    echo "   - النسخ اليومي: " . ($settings->daily_backup ? 'مُفعل' : 'مُعطل') . "\n";
    echo "   - مسار النسخ: {$settings->backup_path}\n";
    echo "   - الاحتفاظ بالنسخ: {$settings->keep_backups_days} يوم\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل إعدادات النسخ: " . $e->getMessage() . "\n";
}
echo "\n";

// 9. اختبار Observer
echo "9️⃣ اختبار Observer...\n";
try {
    $observerFile = 'app/Observers/ClientObserver.php';
    if (file_exists($observerFile)) {
        echo "✅ ClientObserver موجود\n";
        
        // فحص تسجيل Observer
        $providerFile = 'app/Providers/AppServiceProvider.php';
        $content = file_get_contents($providerFile);
        if (strpos($content, 'ClientObserver') !== false) {
            echo "✅ Observer مسجل في AppServiceProvider\n";
        } else {
            echo "⚠️  Observer غير مسجل في AppServiceProvider\n";
        }
    } else {
        echo "❌ ClientObserver مفقود\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص Observer: " . $e->getMessage() . "\n";
}
echo "\n";

// 10. اختبار JavaScript
echo "🔟 اختبار JavaScript...\n";
$jsFile = 'public/assets/js/notifications.js';
if (file_exists($jsFile)) {
    $jsContent = file_get_contents($jsFile);
    $jsChecks = [
        'NotificationSystem' => 'كلاس النظام الرئيسي',
        'showDesktopNotification' => 'إشعارات سطح المكتب',
        'playNotificationSound' => 'تشغيل الأصوات',
        'checkForNewNotifications' => 'فحص الإشعارات الجديدة',
        'createTestNotification' => 'إنشاء إشعار تجريبي'
    ];
    
    foreach ($jsChecks as $function => $description) {
        if (strpos($jsContent, $function) !== false) {
            echo "✅ {$description}: موجود\n";
        } else {
            echo "❌ {$description}: مفقود\n";
        }
    }
} else {
    echo "❌ ملف JavaScript مفقود\n";
}
echo "\n";

// 11. اختبار CSS
echo "1️⃣1️⃣ اختبار CSS...\n";
$cssFile = 'public/assets/css/notifications.css';
if (file_exists($cssFile)) {
    $cssSize = filesize($cssFile);
    echo "✅ ملف CSS موجود ({$cssSize} بايت)\n";
    
    $cssContent = file_get_contents($cssFile);
    $cssClasses = [
        '.notification-bell',
        '.notification-count-badge',
        '.notification-dropdown',
        '.notification-toast'
    ];
    
    foreach ($cssClasses as $class) {
        if (strpos($cssContent, $class) !== false) {
            echo "✅ كلاس {$class}: موجود\n";
        } else {
            echo "❌ كلاس {$class}: مفقود\n";
        }
    }
} else {
    echo "❌ ملف CSS مفقود\n";
}
echo "\n";

// 12. تقرير نهائي
echo "📊 التقرير النهائي:\n";
echo "=" . str_repeat("=", 30) . "\n";

// إحصائيات
$totalNotifications = DB::table('notifications')->count();
$unreadNotifications = DB::table('notifications')->where('is_read', false)->count();
$totalBackups = DB::table('backup_logs')->count();
$successfulBackups = DB::table('backup_logs')->where('status', 'success')->count();

echo "📈 الإحصائيات:\n";
echo "   - إجمالي الإشعارات: {$totalNotifications}\n";
echo "   - الإشعارات غير المقروءة: {$unreadNotifications}\n";
echo "   - إجمالي النسخ الاحتياطية: {$totalBackups}\n";
echo "   - النسخ الناجحة: {$successfulBackups}\n\n";

echo "🎯 الخطوات التالية:\n";
echo "1. زيارة /notifications لاختبار واجهة الإشعارات\n";
echo "2. زيارة /notifications/settings لتخصيص الإعدادات\n";
echo "3. زيارة /backup لإدارة النسخ الاحتياطي\n";
echo "4. إضافة ملفات الأصوات في public/assets/sounds/\n";
echo "5. اختبار إضافة عميل جديد لتجربة الإشعارات\n\n";

echo "🎉 انتهى الاختبار الشامل!\n";
echo "💡 في حالة وجود مشاكل، راجع الملفات المفقودة أعلاه\n";

?>
