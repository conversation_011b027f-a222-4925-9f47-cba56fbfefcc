<?php

namespace App\Console\Commands;

use App\Models\BackupSetting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class DailyBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'backup:daily';

    /**
     * The console command description.
     */
    protected $description = 'إنشاء نسخة احتياطية يومية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $settings = BackupSetting::getSettings();

        if (!$settings->auto_backup_enabled || !$settings->daily_backup) {
            $this->info('النسخ الاحتياطي اليومي معطل');
            return 0;
        }

        $this->info('بدء النسخ الاحتياطي اليومي...');

        return Artisan::call('backup:create', [
            '--type' => 'daily'
        ]);
    }
}
