<?php

namespace App\Http\Controllers;


use App\Models\home;
use App\Models\moneys;
use App\Models\visits;
use App\Models\clients;
use Illuminate\Http\Request;
use App\Models\infocompanies;
use Illuminate\Support\Facades\Route;


class HomeController extends Controller
{



    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        $home = home::all();
        $infocompanies = infocompanies::all();

        $visits = visits::all();

        $moneys = moneys::all()->sortByDesc("id");

        $clients = clients::all()->sortByDesc("id");

        return view('home.home', compact('infocompanies','moneys','visits','clients','home'));




    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(home $home)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(home $home)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, home $home)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(home $home)
    {
        //
    }
}
