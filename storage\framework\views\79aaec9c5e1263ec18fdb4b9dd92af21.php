<?php $__env->startSection('title'); ?>
    الزبائن ..
    <?php
        $infocompanies = DB::table('infocompanies')->get();
    ?>
    <?php echo e($infocompanies->implode('titleofcompany')); ?>


<?php $__env->stopSection(); ?>
<?php $__env->startSection('css'); ?>

    <!-- Internal Data table css -->
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/prism/prism.css')); ?>" rel="stylesheet">
    <!---Internal Owl Carousel css-->
    <link href="<?php echo e(URL::asset('assets/plugins/owl-carousel/owl.carousel.css')); ?>" rel="stylesheet">
    <!---Internal  Multislider css-->
    <link href="<?php echo e(URL::asset('assets/plugins/multislider/multislider.css')); ?>" rel="stylesheet">
    <!--- Select2 css -->
    <link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">


    <link href="<?php echo e(URL::asset('assets/plugins/select2/css/bootstrap.min.css')); ?>" rel="stylesheet">




    <style>
        /* Media Query for Mobile Devices */
        @media (max-width: 480px) {
            h1 {
                color: red;
            }
        }

        /* Media Query for low resolution  Tablets, Ipads */
        @media (min-width: 481px) and (max-width: 767px) {
            h1 {
                color: yellow;
            }
        }

        /* Media Query for Tablets Ipads portrait mode */
        @media (min-width: 768px) and (max-width: 1024px) {
            h1 {
                color: blue;
            }
        }

        /* Media Query for Laptops and Desktops */
        @media (min-width: 1025px) and (max-width: 1280px) {
            h1 {
                color: green;
            }
        }

        /* Media Query for Large screens */
        @media (min-width: 1281px) {
            h1 {
                color: rgb(66, 7, 161);
            }
        }
    </style>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('page-header'); ?>
    <!-- breadcrumb -->
    <div class="breadcrumb-header justify-content-between">
        <div class="my-auto">
            <div class="d-flex">
                <h4 class="content-title mb-0 my-auto">الزبائن</h4><span class="text-muted mt-1 tx-13 mr-2 mb-0">/ قائمة
                    الزبائن</span>
            </div>
        </div>

        <!--

                                                                                                                            <div class="d-flex my-xl-auto right-content">
                                                                                                                                <div class="pr-1 mb-3 mb-xl-0">
                                                                                                                                    <button type="button" class="btn btn-info btn-icon ml-2"><i class="mdi mdi-filter-variant"></i></button>
                                                                                                                                </div>
                                                                                                                                <div class="pr-1 mb-3 mb-xl-0">
                                                                                                                                    <button type="button" class="btn btn-danger btn-icon ml-2"><i class="mdi mdi-star"></i></button>
                                                                                                                                </div>
                                                                                                                                <div class="pr-1 mb-3 mb-xl-0">
                                                                                                                                    <button type="button" class="btn btn-warning  btn-icon ml-2"><i class="mdi mdi-refresh"></i></button>
                                                                                                                                </div>

                                                                                                                    <div class="mb-3 mb-xl-0">
                                                                                                                                    <div class="btn-group dropdown">
                                                                                                                                        <button type="button" class="btn btn-primary">14 Aug 2019</button>
                                                                                                                                        <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" id="dropdownMenuDate" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                                                                                        <span class="sr-only">Toggle Dropdown</span>
                                                                                                                                        </button>
                                                                                                                                        <div class="dropdown-menu dropdown-menu-left" aria-labelledby="dropdownMenuDate" data-x-placement="bottom-end">
                                                                                                                                            <a class="dropdown-item" href="#">2015</a>
                                                                                                                                            <a class="dropdown-item" href="#">2016</a>
                                                                                                                                            <a class="dropdown-item" href="#">2017</a>
                                                                                                                                            <a class="dropdown-item" href="#">2018</a>
                                                                                                                                        </div>
                                                                                                                                    </div>
                                                                                                                                </div>
                                                                                                                           -->



    </div>
    </div>
    <!-- breadcrumb -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <!-- row -->
    <!--<div class="row">-->
    <!-- row opened -->



    <div class="row row-sm">

        <!--/div-->

        <!--div-->
        <div class="col-xl-12">

            <?php if($errors->any()): ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if(session()->has('Add')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <strong><?php echo e(session()->get('Add')); ?></strong>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <?php if(session()->has('delete')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <strong><?php echo e(session()->get('delete')); ?></strong>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <?php if(session()->has('edit')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <strong><?php echo e(session()->get('edit')); ?></strong>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>



            <!-- row opened -->
            <div class="row row-sm">

                

                <?php

                    // $date1 = new DateTime($infocompanies->implode('dateofinsertofactive'));
                    // $date2 = new DateTime($infocompanies->implode('dateofendofactive'));
                    // $interval = $date2->diff($date1);

                    $fdate = $infocompanies->implode('dateofinsertofactive');
                    $tdate = $infocompanies->implode('dateofendofactive');
                    $datetime1 = strtotime($fdate); // convert to timestamps
                    $datetime2 = strtotime($tdate); // convert to timestamps
                    $days = (int) (($datetime2 - $datetime1) / 86400); // will give the difference in days , 86400 is the timestamp difference of a day

                ?>
                <?php

if ($days < 3) {
// exit program normally
//Redirect::url("/logout")
echo "<script>alert('  .... ضروري جدا  ....لقد بقي من وقت التفعيل  (  $days  )   أيام')</script>";

Auth::logout();
Session::flush();

echo "<script> window.location='login'</script>";

return Redirect::to('login');
return Redirect::to('home');
exit();
}

if ($days < 15) {
// exit program normally
//Redirect::url("/logout")
echo "<script>alert(' لقد بقي من وقت التفعيل (  $days  ) أيام وعلى ذلك الرجاء التجديد قبل وصول البرنامج ل 3 أيام')</script>";




?>



                <div class="alert alert-danger">
                    <strong> الرجاء الحذر! </strong> لقد بقي من وقت التفعيل ( <?php echo e($days); ?> ) أيام

                    وعلى ذلك الرجاء التجديد قبل وصول البرنامج ل 3 ايام
                </div>


                <?php
}
?>



                
                <div class="col-xl-12">
                    <div class="card">
                        <div class="card-header pb-0">
                            <div class="d-flex justify-content-between">
                                <h4 class="card-title mg-b-0">قائمة الزبائن <img alt="قائمة الزبائن" title="قائمة الزبائن"
                                        src="<?php echo e(URL::asset('assets/img/media/jalal/team.png')); ?>" class=""></h4>
                                <i class="mdi mdi-dots-horizontal text-gray"></i>
                            </div>

                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <a class="modal-effect btn btn-outline-primary btn-block" data-effect="effect-scale"
                                    data-toggle="modal" href="#modaldemo8">إضافة زبون<img alt="إضافة زبون"
                                        title="إضافة زبون" src="<?php echo e(URL::asset('assets/img/media/jalal/add_user.png')); ?>"
                                        class=""></a>

                                <table class="table" style="max-width:500px" id="example76">




                                    <thead>
                                        <tr style="text-align: center">









                                            <th scope="col">العمليات</th>
                                            <th scope="col">استبيان</th>
                                            <th scope="col">المعرف</th>
                                            <th scope="col">الاسم</th>
                                            <th scope="col">الجنس</th>
                                            <th scope="col">الهاتف أو الموبايل</th>
                                            <th scope="col">تاريخ انشاء الحساب</th>
                                            <th scope="col">ملاحظة</th>

                                        </tr>
                                    </thead>
                                    <tbody>

                                        <?php $i = 0; ?>
                                        <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $e): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php $i++; ?>
                                            <?php echo e($e->clients); ?>




                                            <tr style="text-align: center">









                                                <td>

                                                    <a class="modal-effect btn btn-sm btn-info" data-effect="effect-scale"
                                                        data-id="<?php echo e($e->id); ?>" data-name="<?php echo e($e->name); ?>"
                                                        data-gender="<?php echo e($e->gender); ?>"
                                                        data-phone="<?php echo e($e->phone); ?>"
                                                        data-subscroptiondate="<?php echo e($e->subscroptiondate); ?>"
                                                        data-note="<?php echo e($e->note); ?>"
                                                        data-password="<?php echo e($e->password); ?>"
                                                        data-roles_name="<?php echo e($e->roles_name); ?>"
                                                        data-status="<?php echo e($e->status); ?>"
                                                        data-active="<?php echo e($e->active); ?>" data-toggle="modal"
                                                        href="#exampleModal2" title="تعديل"><i
                                                            class="las la-pen"></i></a>

                                                    <a class="modal-effect btn btn-sm btn-danger"
                                                        data-effect="effect-scale" data-id="<?php echo e($e->id); ?>"
                                                        data-name="<?php echo e($e->name); ?>" data-toggle="modal"
                                                        href="#modaldemo9" title="حذف"><i
                                                            class="las la-trash"></i></a>

                                                </td>





                                                <?php

           //  $clientvaid= $e->id;
           //  $dataclientvaid = DB::table('clients')->where(array('id' => $clientvaid))->first();

          //if($dataclientvaid !='1')



                                                $clientvaid= $e->id;

                                                $dataclientvaid = DB::table('hed_estps')->where(array('clients_id' => $clientvaid))->count();

                                                 if($dataclientvaid != 1)

                                                        {
                                                            ?>
                                                <td> <a class="modal-effect btn btn-outline-primary btn-block"
                                                        data-effect="effect-scale" data-toggle="modal"
                                                        data-id="<?php echo e($e->id); ?>" data-name="<?php echo e($e->name); ?>"
                                                        href="#modaldemo18">إستبيان</a>
                                                    <?php
                     }
                                                 else{

                                  ?>
                                                <td> <a class="btn btn-success btn-block" data-effect="effect-scale"
                                                        data-toggle="modal" data-id="<?php echo e($e->id); ?>"
                                                        data-name="<?php echo e($e->name); ?>" href="#"> جاهز</a>
                                                    <?php
                                                        }
             ?>




                                                </td>

                                                <td><?php echo e($i); ?></td>


                                                <td>
                                                    <?php

                                         $clientvaid= $e->id;

                                         $dataclientvaid = DB::table('hed_estps')->where(array('clients_id' => $clientvaid))->count();

                                          if($dataclientvaid != 1)

{
    ?>
                                                    <a style="color:#b3651d;"
                                                        href="<?php echo e(url('estpsdetalsnoe')); ?>/<?php echo e($e->id); ?>"><?php echo e($e->name); ?>

                                                    </a>
                                                    <?php
}
else
{
?>

                                                    <a
                                                        href="<?php echo e(url('estpsdetals')); ?>/<?php echo e($e->id); ?>"><?php echo e($e->name); ?></a>
                                                    <?php
}
 ?>


                                                </td>


                                                <td>
                                                    <?php
                                                    if ($e->gender=="male")
                                                    {
?>


                                                    <img alt="ذكر" title="ذكر"
                                                        src="<?php echo e(URL::asset('assets/img/media/jalal/male.png')); ?>"
                                                        class="">
                                                    <?php
echo "<p style='color: red;'>ذكر</p>";
}




                                                    if ($e->gender=="female")
{
?>
                                                    <img alt="انثى" title="انثى"
                                                        src="<?php echo e(URL::asset('assets/img/media/jalal/female.png')); ?>"
                                                        class="">
                                                    <?php
                                                        echo "<p style='color: blue;'>انثى</p>";

}



                                                    ?>



                                                </td>

                                                <td><?php echo e($e->phone); ?></td>



                                                <td><?php echo e($e->subscroptiondate); ?></td>

                                                <td><?php echo e($e->note); ?></td>






                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>




                                </table>

                            </div>
                        </div>
                    </div>
                    <!--/div-->

                </div>












                <!-- بداية الاستبيان -->

                <div class="modal" id="modaldemo18" data-backdrop="static" tabindex="-1" role="dialog"
                    aria-labelledby="staticBackdropLabel" aria-hidden="true">>
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content modal-content-demo">
                            <div class="modal-header">
                                <h6 class="modal-title">إضافة إستبيان</h6><button aria-label="Close" class="close"
                                    data-dismiss="modal" type="button"><span aria-hidden="true">&times;</span></button>
                            </div>
                            <div class="modal-body">



                                <form action="<?php echo e(route('estps.store')); ?>" method="post">
                                    <?php echo e(csrf_field()); ?>

                                    <div class="form-group">
                                        <?php
                                        $maxId = DB::table('hed_estps')->max('id');
                                        $maxmaxId = $maxId + '1';
                                        
                                        ?>

                                        <input type="hidden" value="<?php echo e($maxmaxId); ?>" class="form-control"
                                            id="estps_id" name="estps_id" required>

                                        <input type="hidden" class="form-control" id="id" name="id"
                                            required>


                                        <label style="width: 400px; " for="exampleInputEmail1">الاسم</label>

                                        <input style="width:50%; font-size: 20px; background-color: rgba(1,1,51,0.1);"
                                            type="text" class="form-control" id="name" name="name" required
                                            readonly="readonly">
                                        <br>
                                        <hr>

                                        <?php $i = 0; ?>
                                        <?php $__currentLoopData = $ques; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $x): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php $i++; ?>
                                            <?php echo e($x->ques); ?>


                                            <tr>
                                                <td></td>



                                                <input type="hidden" class="form-control"
                                                    id="question<?php echo e($i); ?>" name="question<?php echo e($i); ?>"
                                                    value="  <?php echo e($x->question); ?>">

                                                &nbsp السؤال <?php echo e($i); ?> : &nbsp <label
                                                    style="width:70%; font-size: 20px;  background-color: rgba(0,0,0,0);"
                                                    for="exampleInputEmail1"> <?php echo e($x->question); ?> ؟</label>








                                                <!-- Bootstrap 4: Radio Button 1 -->
                                                <div class="input-group mb-5">
                                                    <div class="form-control">
                                                        <input class="form-control" type="radio"
                                                            name="answer<?php echo e($i); ?>"
                                                            id="answer<?php echo e($i); ?>" value="yes">
                                                        <label>
                                                            نعم
                                                        </label>
                                                    </div>

                                                    <!-- Bootstrap 4: Radio Button 2 -->
                                                    <div class="form-control">
                                                        <input class="form-control" type="radio"
                                                            name="answer<?php echo e($i); ?>"
                                                            id="answer<?php echo e($i); ?>" value="no">
                                                        <label>
                                                            لا
                                                        </label>
                                                    </div>

                                                    <div class="form-control">
                                                        <input class="form-control" type="radio"
                                                            name="answer<?php echo e($i); ?>"
                                                            id="answer<?php echo e($i); ?>" value="yesno"checked>
                                                        <label>
                                                            غير معروف


                                                        </label>
                                                    </div>

                                                    <div style="margin: auto;"">


                                                        &nbsp السبب ؟ &nbsp

                                                    </div>

                                                    <input style="width:70%;  background-color: rgba(0,0,55,0.1);"
                                                        type="text" class="form-control" id="note<?php echo e($i); ?>"
                                                        name="note<?php echo e($i); ?>">
                                                </div>
                                                <hr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                        </tr>
                                        <br><br>

                                    </div>
                                    <div class="modal-footer">

                                        <button type="submit" class="btn btn-success">تاكيد</button>



                                        <button class="btn ripple btn-secondary" data-dismiss="modal"
                                            type="button">اغلاق</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>





                </div>

            </div>

        </div>
        <!-- نهاية الاستبيان -->
    </div>



    <!-- Basic modal -->
    <div class="modal" id="modaldemo8">
        <div class="modal-dialog" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h6 class="modal-title">إضافة زبون</h6><button aria-label="Close" class="close"
                        data-dismiss="modal" type="button"><span aria-hidden="true">&times;</span></button>

                </div>

                <div class="modal-body">

                    <form action="<?php echo e(route('clients.store')); ?>" method="post">
                        <?php echo e(csrf_field()); ?>

                        <div class="form-group">

                            <label for="exampleInputEmail1">الاسم</label>
                            <input onfocus="this.value=''" value="ضع الاسم هنا" type="text" class="form-control"
                                id="name" name="name" required>



                            <label for="exampleInputEmail1">الجنس</label>


                            <br>


                            <!-- Bootstrap 4: Radio Button 1 -->
                            <div class="input-group mb-3">


                                <!-- Bootstrap 4: Radio Button 2 -->
                                <label>
                                    ذكر
                                </label>

                                <input class="form-control" type="radio" name="gender" id="gender"
                                    value="male">

                                <label>
                                    انثى
                                </label>

                                <input class="form-control" type="radio" name="gender" id="gender" value="female"
                                    checked>



                            </div>
                            <div class="alert alert-danger" role="alert">

                                عند اضافة رقم الجوال الرجاء عدم وضع رقم صفر (0)

                            </div>

                            <label for="exampleInputEmail1">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phone" name="phone" required>


                            <br>




                            <label for="exampleInputEmail1">تاريخ انشاء الحساب</label>
                            <input type="date" value="<?php echo date('Y-m-d'); ?>" class="form-control"
                                id="subscroptiondate" name="subscroptiondate">


                            <label for="exampleInputEmail1">ملاحظة</label>

                            <input id="note" name="note" type="text" class="form-control">

                            <!--
                                                                                                                                                <label for="exampleInputEmail1">كلمة المرور</label>
                                                                                                                                                <input  type="password" class="form-control" id="password" name="password" value="123456" required>
                                                                                                                                                -->


                            <input type="hidden" class="form-control" id="roles_name" name="roles_name" value="yes"
                                required>

                            <input type="hidden" class="form-control" id="active" name="active" value="yes"
                                required>

                            <input type="hidden" class="form-control" id="status" name="status" value="yes"
                                required>



                            <!--

                                                                                                                                                <label for="exampleInputEmail1">الدور</label>


                                                                                                                                                <select class="form-control" name="roles_name" id="roles_name">
                                                                                                                                                    <option value="yes">نعم</option>
                                                                                                                                                    <option value="no" >لا</option>
                                                                                                                                                </select>


                                                                                                                                                <label for="exampleInputEmail1">مفعل</label>

                                                                                                                                                <select class="form-control" name="active" id="active">
                                                                                                                                                    <option value="yes">نعم</option>
                                                                                                                                                    <option value="no" >لا</option>
                                                                                                                                                </select>




                                                                                                                                                <label for="exampleInputEmail1">الحالة</label>


                                                                                                                                                <select class="form-control" name="status" id="status">

                                                                                                                                                    <option value="yes">نعم</option>
                                                                                                                                                    <option value="no" >لا</option>
                                                                                                                                                </select>


                                                                                                                                            -->




                        </div>




                        <div class="modal-footer">
                            <button type="submit" class="btn btn-success">تاكيد</button>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                        </div>
                    </form>

                </div>

            </div>

        </div>
        <!-- End Basic modal -->








    </div>

    </div>

    <!-- edit -->
    <div class="modal fade" id="exampleModal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModal2">تعديل معلومات الزبون</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                


                <form action='<?php echo e(url('clients/update')); ?>' method="post" autocomplete="off">
                    <?php echo e(method_field('patch')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">

                        <div class="form-group">
                            <label for="title">الاسم :</label>
                            <input type="hidden" class="form-control" name="id" id="id" value="">
                            <input type="text" class="form-control" name="name" id="name">


                            <label for="title">النوع :</label>



                            <select class="form-control" name="gender" id="gender">


                                <option value="male">ذكر </option>
                                <option value="female"> انثى</option>
                            </select>

                            <div class="alert alert-danger" role="alert">

                                عند اضافة رقم الجوال الرجاء عدم وضع رقم صفر (0)

                            </div>

                            <label for="title">رقم الهاتف :</label>
                            <input type="text" class="form-control" name="phone" id="phone">

                            <label for="title">تاريخ انشاء الحساب :</label>
                            <input type="date" class="form-control" name="subscroptiondate" id="subscroptiondate">
                            <label for="title">ملاحظة :</label>
                            <input type="text" class="form-control" name="note" id="note">







                            <input type="hidden" class="form-control" name="password" id="password">


                            <input type="hidden" class="form-control" name="roles_name" id="roles_name">

                            <input type="hidden" class="form-control" name="active" id="active">

                            <input type="hidden" class="form-control" name="status" id="status">






                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
    <!-- delete -->
    <div class="modal fade" id="modaldemo9" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حذف القسم</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                

                <form action="<?php echo e(url('clients/destroy')); ?>" method="post">
                    <?php echo e(method_field('delete')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">
                        <p>هل انت متاكد من حذف هذا الاسم ؟</p><br>
                        <input type="hidden" name="id" id="id" value="">
                        <input class="form-control" name="name" id="name" type="text" readonly>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">الغاء</button>
                        <button type="submit" class="btn btn-danger">تاكيد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- /row -->
    </div>
    <!-- row closed -->
    </div>
    <!-- Container closed -->
    </div>
    <!-- main-content closed -->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <!-- Internal Data tables -->
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jszip.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/pdfmake.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/vfs_fonts1.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.print.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/bootstrap.bundle.min.js')); ?>"></script>
    <!--Internal  Datatable js -->
    <script src="<?php echo e(URL::asset('assets/js/table-data.js')); ?>"></script>


    <script src="/p/pdfmake.js"></script>
    <script src="/p/vfs_fonts.js"></script>


    <!-- Internal Prism js-->
    <script src="<?php echo e(URL::asset('assets/plugins/prism/prism.js')); ?>"></script>
    <!--Internal  Datepicker js -->
    <script src="<?php echo e(URL::asset('assets/plugins/jquery-ui/ui/widgets/datepicker.js')); ?>"></script>
    <!-- Internal Select2 js-->
    <script src="<?php echo e(URL::asset('assets/plugins/select2/js/select2.min.js')); ?>"></script>
    <!-- Internal Modal js-->
    <script src="<?php echo e(URL::asset('assets/js/modal.js')); ?>"></script>



    <script>
        $('#exampleModal2').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var name = button.data('name')
            var gender = button.data('gender')
            var phone = button.data('phone')
            var subscroptiondate = button.data('subscroptiondate')
            var note = button.data('note')
            var password = button.data('password')
            var roles_name = button.data('roles_name')
            var status = button.data('status')
            var active = button.data('active')



            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #name').val(name);
            modal.find('.modal-body #gender').val(gender);
            modal.find('.modal-body #phone').val(phone);
            modal.find('.modal-body #subscroptiondate').val(subscroptiondate);
            modal.find('.modal-body #note').val(note);
            modal.find('.modal-body #password').val(password);

            modal.find('.modal-body #roles_name').val(roles_name);
            modal.find('.modal-body #status').val(status);
            modal.find('.modal-body #active').val(active);


        })





        $('#modaldemo9').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var name = button.data('name')

            var modal = $(this)

            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #name').val(name);
        })
    </script>


    <script>
        $('#example76').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>

    <script>
        $('#modaldemo18').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var name = button.data('name')




            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #name').val(name);



        })



        $('#modaldemo9').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var name = button.data('name')

            var modal = $(this)

            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #name').val(name);
        })
    </script>
    <script></script>



<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nc\resources\views/clients/clients.blade.php ENDPATH**/ ?>