<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->boolean('sound_enabled')->default(true); // تفعيل الصوت
            $table->string('sound_file')->default('notification.mp3'); // ملف الصوت
            $table->integer('sound_volume')->default(50); // مستوى الصوت (0-100)
            $table->boolean('desktop_notifications')->default(true); // إشعارات سطح المكتب
            $table->boolean('new_client_notifications')->default(true); // إشعارات العملاء الجدد
            $table->boolean('new_visit_notifications')->default(true); // إشعارات الزيارات الجديدة
            $table->boolean('payment_notifications')->default(true); // إشعارات المدفوعات
            $table->boolean('challenge_notifications')->default(true); // إشعارات التحديات
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_settings');
    }
};
