/**
 * نظام الإشعارات المتقدم
 * يتضمن إشعارات فورية، أصوات، وإشعارات سطح المكتب
 */

class NotificationSystem {
    constructor() {
        this.settings = {
            soundEnabled: true,
            soundFile: 'notification.mp3',
            soundVolume: 50,
            desktopNotifications: true,
            checkInterval: 180000 // 3 دقائق (180 ثانية)
        };

        this.audio = null;
        this.lastNotificationId = 0;
        this.isInitialized = false;

        this.init();
    }

    /**
     * تهيئة النظام
     */
    init() {
        this.loadSettings();
        this.setupAudio();
        this.requestDesktopPermission();
        this.bindEvents();
        this.startPolling();
        this.isInitialized = true;

        console.log('نظام الإشعارات تم تهيئته بنجاح');
    }

    /**
     * تحميل الإعدادات من الخادم
     */
    async loadSettings() {
        try {
            const response = await fetch('/notifications/settings/get');
            if (response.ok) {
                const data = await response.json();
                this.settings = { ...this.settings, ...data };
            }
        } catch (error) {
            console.warn('فشل في تحميل إعدادات الإشعارات:', error);
            // استخدام الإعدادات الافتراضية
            this.settings = {
                soundEnabled: true,
                soundFile: 'notification.mp3',
                soundVolume: 50,
                desktopNotifications: true,
                checkInterval: 180000 // 3 دقائق افتراضي
            };
        }
    }

    /**
     * إعداد الصوت
     */
    setupAudio() {
        if (this.settings.soundEnabled) {
            this.audio = new Audio(`/assets/sounds/${this.settings.soundFile}`);
            this.audio.volume = this.settings.soundVolume / 100;
        }
    }

    /**
     * طلب إذن إشعارات سطح المكتب
     */
    async requestDesktopPermission() {
        if (this.settings.desktopNotifications && 'Notification' in window) {
            if (Notification.permission === 'default') {
                try {
                    const permission = await Notification.requestPermission();
                    if (permission === 'granted') {
                        console.log('تم منح إذن الإشعارات');
                        // إظهار إشعار ترحيبي
                        this.showWelcomeNotification();
                    } else {
                        console.warn('تم رفض إذن الإشعارات');
                    }
                } catch (error) {
                    console.error('خطأ في طلب إذن الإشعارات:', error);
                }
            } else if (Notification.permission === 'granted') {
                console.log('إذن الإشعارات ممنوح مسبقاً');
            }
        }
    }

    /**
     * إظهار إشعار ترحيبي
     */
    showWelcomeNotification() {
        const welcomeNotification = new Notification('🎉 مرحباً بك!', {
            body: 'تم تفعيل إشعارات سطح المكتب بنجاح.\nستتلقى إشعارات عند حدوث أنشطة جديدة.',
            icon: '/assets/img/logo.png',
            tag: 'welcome-notification'
        });

        setTimeout(() => {
            welcomeNotification.close();
        }, 4000);
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // النقر على قائمة الإشعارات
        document.getElementById('notificationDropdown')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.loadNotifications();
        });

        // تحديد جميع الإشعارات كمقروءة
        document.getElementById('markAllRead')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.markAllAsRead();
        });

        // زر التحديث
        document.getElementById('refreshNotifications')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.checkForNewNotifications();
            this.loadNotifications();

            // تأثير بصري للتحديث
            const icon = e.target.querySelector('i') || e.target;
            icon.classList.add('fa-spin');
            setTimeout(() => {
                icon.classList.remove('fa-spin');
            }, 1000);
        });

        // زر الاختبار في الهيدر
        document.getElementById('testNotificationBtn')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.createTestNotification();
        });

        // إغلاق الإشعار عند النقر عليه
        document.addEventListener('click', (e) => {
            if (e.target.closest('.notification-item')) {
                const notificationId = e.target.closest('.notification-item').dataset.id;
                if (notificationId) {
                    this.markAsRead(notificationId);
                }
            }
        });
    }

    /**
     * بدء التحقق الدوري من الإشعارات الجديدة
     */
    startPolling() {
        this.checkForNewNotifications();

        // إيقاف التحقق السابق إذا كان موجوداً
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }

        // بدء التحقق الجديد
        this.pollingInterval = setInterval(() => {
            this.checkForNewNotifications();
        }, this.settings.checkInterval);

        console.log(`تم بدء التحقق من الإشعارات كل ${this.settings.checkInterval / 1000} ثانية`);
    }

    /**
     * تحديث وقت التحقق من الإشعارات
     */
    updateCheckInterval(intervalInSeconds) {
        this.settings.checkInterval = intervalInSeconds * 1000;
        this.startPolling(); // إعادة بدء التحقق بالوقت الجديد
        console.log(`تم تحديث وقت التحقق إلى ${intervalInSeconds} ثانية`);
    }

    /**
     * التحقق من الإشعارات الجديدة
     */
    async checkForNewNotifications() {
        try {
            const response = await fetch('/notifications/unread');
            if (response.ok) {
                const data = await response.json();

                this.updateNotificationUI({
                    count: data.count,
                    notifications: data.notifications
                });

                // التحقق من وجود عملاء جدد
                if (data.notifications.length > 0) {
                    const newClients = data.notifications.filter(n => {
                        const clientId = n.id.replace('client_', '');
                        return clientId > this.lastNotificationId;
                    });

                    if (newClients.length > 0) {
                        this.handleNewNotifications(newClients);
                        // تحديث آخر ID للعميل
                        const maxClientId = Math.max(...data.notifications.map(n =>
                            parseInt(n.id.replace('client_', ''))
                        ));
                        this.lastNotificationId = maxClientId;
                    }
                }
            }
        } catch (error) {
            console.error('خطأ في التحقق من الإشعارات:', error);
        }
    }

    /**
     * معالجة الإشعارات الجديدة
     */
    handleNewNotifications(notifications) {
        notifications.forEach(notification => {
            this.showDesktopNotification(notification);
            this.playNotificationSound();
            this.showInAppNotification(notification);
        });

        // تحديث قائمة الإشعارات فوراً
        this.loadNotifications();
    }

    /**
     * عرض إشعار سطح المكتب
     */
    showDesktopNotification(notification) {
        if (!this.settings.desktopNotifications || !('Notification' in window) || Notification.permission !== 'granted') {
            return;
        }

        // تحديد الأيقونة حسب نوع الإشعار
        let iconPath = '/assets/img/logo.png';
        if (notification.type === 'new_client') {
            iconPath = '/assets/img/client-icon.png';
        } else if (notification.type === 'backup_success') {
            iconPath = '/assets/img/backup-icon.png';
        }

        const desktopNotification = new Notification(`🔔 ${notification.title}`, {
            body: `${notification.message}\n\n📅 ${new Date().toLocaleString('ar-SA')}`,
            icon: iconPath,
            badge: '/assets/img/logo.png',
            tag: `notification-${notification.id}`,
            requireInteraction: false,
            silent: false,
            timestamp: Date.now(),
            data: {
                notificationId: notification.id,
                type: notification.type,
                url: window.location.origin + '/notifications'
            }
        });

        desktopNotification.onclick = () => {
            window.focus();
            // فتح صفحة الإشعارات
            window.location.href = '/notifications';
            this.markAsRead(notification.id);
            desktopNotification.close();
        };

        desktopNotification.onshow = () => {
            // تأثير صوتي إضافي للإشعارات المهمة
            if (notification.type === 'new_client') {
                this.playNotificationSound();
            }
        };

        // إغلاق تلقائي بعد 8 ثوان
        setTimeout(() => {
            desktopNotification.close();
        }, 8000);
    }

    /**
     * تشغيل صوت الإشعار
     */
    playNotificationSound() {
        if (!this.settings.soundEnabled) return;

        try {
            // محاولة تشغيل الملف الصوتي أولاً
            if (this.audio) {
                this.audio.currentTime = 0;
                this.audio.play().catch(error => {
                    console.warn('فشل في تشغيل الملف الصوتي، استخدام الصوت المولد:', error);
                    this.playGeneratedSound();
                });
            } else {
                // إنشاء صوت جديد
                const audio = new Audio(`/assets/sounds/${this.settings.soundFile}`);
                audio.volume = this.settings.soundVolume / 100;
                audio.play().catch(error => {
                    console.warn('فشل في تشغيل الملف الصوتي، استخدام الصوت المولد:', error);
                    this.playGeneratedSound();
                });
            }
        } catch (error) {
            console.warn('خطأ في تشغيل الصوت، استخدام البديل:', error);
            this.playGeneratedSound();
        }
    }

    /**
     * تشغيل صوت مولد بسيط
     */
    playGeneratedSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // تحديد التردد حسب نوع الصوت
            let frequency = 800;
            const soundType = this.settings.soundFile.replace('.mp3', '');
            switch (soundType) {
                case 'bell': frequency = 1000; break;
                case 'chime': frequency = 900; break;
                case 'ding': frequency = 1200; break;
                case 'pop': frequency = 600; break;
                case 'swoosh': frequency = 400; break;
                default: frequency = 800;
            }

            oscillator.frequency.value = frequency;
            oscillator.type = "sine";

            const volume = this.settings.soundVolume / 100 * 0.3;
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            console.warn('فشل في إنشاء الصوت المولد:', error);
        }
    }

    /**
     * عرض إشعار داخل التطبيق
     */
    showInAppNotification(notification) {
        // إنشاء عنصر الإشعار
        const notificationElement = document.createElement('div');
        notificationElement.className = `notification-toast ${notification.color}`;

        notificationElement.innerHTML = `
            <div class="notification-toast-content">
                <div class="notification-toast-icon bg-${notification.color}">
                    <i class="fa ${notification.icon}"></i>
                </div>
                <div class="notification-toast-text">
                    <h6 class="notification-toast-title">${notification.title}</h6>
                    <p class="notification-toast-message">${notification.message}</p>
                </div>
                <button class="notification-toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <div class="notification-toast-progress"></div>
        `;

        document.body.appendChild(notificationElement);

        // تأثير الدخول
        setTimeout(() => {
            notificationElement.style.transform = 'translateX(0)';
            notificationElement.style.opacity = '1';
        }, 100);

        // إزالة تلقائية بعد 5 ثوان
        setTimeout(() => {
            if (notificationElement.parentNode) {
                notificationElement.style.transform = 'translateX(-100%)';
                notificationElement.style.opacity = '0';
                setTimeout(() => {
                    notificationElement.remove();
                }, 300);
            }
        }, 5000);

        // النقر للانتقال للإشعارات
        notificationElement.addEventListener('click', () => {
            window.location.href = '/notifications';
        });
    }

    /**
     * تحديث واجهة الإشعارات
     */
    updateNotificationUI(data) {
        const countElement = document.getElementById('notificationCount');
        const subtextElement = document.getElementById('notificationSubtext');
        const pulseElement = document.getElementById('notificationPulse');

        if (countElement) {
            if (data.count > 0) {
                countElement.textContent = data.count > 99 ? '99+' : data.count;
                countElement.style.display = 'flex';

                // إضافة نبضة للإشعارات الجديدة
                if (pulseElement) {
                    pulseElement.style.display = 'block';
                }
            } else {
                countElement.style.display = 'none';
                if (pulseElement) {
                    pulseElement.style.display = 'none';
                }
            }
        }

        if (subtextElement) {
            if (data.count > 0) {
                subtextElement.innerHTML = `<i class="fa fa-user-plus"></i> ${data.count} عميل جديد`;
            } else {
                subtextElement.innerHTML = `<i class="fa fa-check-circle"></i> لا توجد عملاء جدد`;
            }
        }
    }

    /**
     * تحميل قائمة الإشعارات
     */
    async loadNotifications() {
        try {
            const response = await fetch('/notifications/unread');
            if (response.ok) {
                const data = await response.json();
                this.renderNotificationList(data.notifications);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
        }
    }

    /**
     * عرض قائمة الإشعارات
     */
    renderNotificationList(notifications) {
        const listElement = document.getElementById('notificationList');
        if (!listElement) return;

        if (notifications.length === 0) {
            listElement.innerHTML = `
                <div class="notification-empty">
                    <i class="fa fa-users"></i>
                    <h5>لا توجد عملاء جدد</h5>
                    <p>ستظهر إشعارات العملاء الجدد هنا</p>
                </div>
            `;
            return;
        }

        listElement.innerHTML = notifications.map(notification => `
            <div class="notification-item ${notification.is_read ? 'read' : 'unread'}" data-id="${notification.id}">
                <div class="d-flex align-items-start">
                    <div class="notification-icon bg-success">
                        <i class="fa fa-user-plus"></i>
                    </div>
                    <div class="notification-content">
                        <h6 class="notification-title-text">
                            👤 عميل جديد
                            ${!notification.is_read ? '<span class="badge badge-success badge-sm mr-2">جديد</span>' : ''}
                        </h6>
                        <p class="notification-message">${notification.message}</p>
                        <div class="notification-time">
                            <i class="fa fa-clock"></i>
                            ${this.formatTime(notification.created_at)}
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديد إشعار كمقروء
     */
    async markAsRead(notificationId) {
        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            const response = await fetch(`/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
                }
            });

            if (response.ok) {
                this.checkForNewNotifications();
            }
        } catch (error) {
            console.error('خطأ في تحديد الإشعار كمقروء:', error);
        }
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    async markAllAsRead() {
        try {
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            const response = await fetch('/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
                }
            });

            if (response.ok) {
                this.checkForNewNotifications();
                this.loadNotifications();
            }
        } catch (error) {
            console.error('خطأ في تحديد جميع الإشعارات كمقروءة:', error);
        }
    }

    /**
     * تنسيق الوقت
     */
    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays < 7) return `منذ ${diffInDays} يوم`;

        return date.toLocaleDateString('ar-SA');
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.setupAudio();
    }

    /**
     * اختبار الصوت
     */
    testSound(soundFile = null, volume = null) {
        const testAudio = new Audio(`/assets/sounds/${soundFile || this.settings.soundFile}`);
        testAudio.volume = (volume || this.settings.soundVolume) / 100;

        // إضافة تأثيرات بصرية أثناء تشغيل الصوت
        const testButton = document.querySelector('#testSoundBtn');
        if (testButton) {
            const originalText = testButton.innerHTML;
            testButton.innerHTML = '<i class="fa fa-volume-up fa-pulse me-2"></i>جاري التشغيل...';
            testButton.disabled = true;

            testAudio.addEventListener('ended', () => {
                testButton.innerHTML = originalText;
                testButton.disabled = false;
            });
        }

        testAudio.play().then(() => {
            // إظهار إشعار نجاح التشغيل
            this.showInAppNotification({
                type: 'test_sound',
                title: '🔊 اختبار الصوت',
                message: 'تم تشغيل الصوت بنجاح!',
                icon: 'fa-volume-up',
                color: 'success'
            });
        }).catch(error => {
            console.warn('فشل في تشغيل الصوت التجريبي:', error);

            // إظهار إشعار فشل التشغيل
            this.showInAppNotification({
                type: 'test_sound_error',
                title: '❌ خطأ في الصوت',
                message: 'فشل في تشغيل الصوت. تحقق من وجود الملف.',
                icon: 'fa-volume-mute',
                color: 'danger'
            });

            if (testButton) {
                testButton.innerHTML = testButton.getAttribute('data-original-text') || 'اختبار الصوت';
                testButton.disabled = false;
            }
        });
    }

    /**
     * إنشاء إشعار تجريبي
     */
    async createTestNotification() {
        try {
            // إنشاء إشعار تجريبي فوري
            const testNotification = {
                id: Date.now(),
                type: 'test',
                title: 'إشعار تجريبي',
                message: 'هذا إشعار تجريبي لاختبار النظام - ' + new Date().toLocaleTimeString('ar-SA'),
                icon: 'fa-flask',
                color: 'warning',
                is_read: false,
                created_at: new Date().toISOString()
            };

            // عرض الإشعار فوراً
            this.showDesktopNotification(testNotification);
            this.showInAppNotification(testNotification);
            this.playNotificationSound();

            // إنشاء الإشعار في قاعدة البيانات
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            const response = await fetch('/notifications/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
                }
            });

            if (response.ok) {
                setTimeout(() => {
                    this.checkForNewNotifications();
                }, 1000);
            }
        } catch (error) {
            console.error('خطأ في إنشاء الإشعار التجريبي:', error);

            // إظهار إشعار خطأ
            this.showInAppNotification({
                type: 'error',
                title: 'خطأ',
                message: 'فشل في إنشاء الإشعار التجريبي',
                icon: 'fa-exclamation-triangle',
                color: 'danger'
            });
        }
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.notificationSystem = new NotificationSystem();
});

// تصدير للاستخدام العام
window.NotificationSystem = NotificationSystem;
