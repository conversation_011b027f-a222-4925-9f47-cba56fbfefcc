/**
 * نظام الإشعارات المتقدم
 * يتضمن إشعارات فورية، أصوات، وإشعارات سطح المكتب
 */

class NotificationSystem {
    constructor() {
        this.settings = {
            soundEnabled: true,
            soundFile: 'notification.mp3',
            soundVolume: 50,
            desktopNotifications: true,
            checkInterval: 30000 // 30 ثانية
        };
        
        this.audio = null;
        this.lastNotificationId = 0;
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * تهيئة النظام
     */
    init() {
        this.loadSettings();
        this.setupAudio();
        this.requestDesktopPermission();
        this.bindEvents();
        this.startPolling();
        this.isInitialized = true;
        
        console.log('نظام الإشعارات تم تهيئته بنجاح');
    }

    /**
     * تحميل الإعدادات من الخادم
     */
    async loadSettings() {
        try {
            const response = await fetch('/notifications/settings/get');
            if (response.ok) {
                const data = await response.json();
                this.settings = { ...this.settings, ...data };
            }
        } catch (error) {
            console.warn('فشل في تحميل إعدادات الإشعارات:', error);
        }
    }

    /**
     * إعداد الصوت
     */
    setupAudio() {
        if (this.settings.soundEnabled) {
            this.audio = new Audio(`/assets/sounds/${this.settings.soundFile}`);
            this.audio.volume = this.settings.soundVolume / 100;
        }
    }

    /**
     * طلب إذن إشعارات سطح المكتب
     */
    requestDesktopPermission() {
        if (this.settings.desktopNotifications && 'Notification' in window) {
            if (Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // النقر على قائمة الإشعارات
        document.getElementById('notificationDropdown')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.loadNotifications();
        });

        // تحديد جميع الإشعارات كمقروءة
        document.getElementById('markAllRead')?.addEventListener('click', () => {
            this.markAllAsRead();
        });

        // إغلاق الإشعار عند النقر عليه
        document.addEventListener('click', (e) => {
            if (e.target.closest('.notification-item')) {
                const notificationId = e.target.closest('.notification-item').dataset.id;
                this.markAsRead(notificationId);
            }
        });
    }

    /**
     * بدء التحقق الدوري من الإشعارات الجديدة
     */
    startPolling() {
        this.checkForNewNotifications();
        setInterval(() => {
            this.checkForNewNotifications();
        }, this.settings.checkInterval);
    }

    /**
     * التحقق من الإشعارات الجديدة
     */
    async checkForNewNotifications() {
        try {
            const response = await fetch('/notifications/unread');
            if (response.ok) {
                const data = await response.json();
                this.updateNotificationUI(data);
                
                // التحقق من وجود إشعارات جديدة
                if (data.notifications.length > 0) {
                    const newNotifications = data.notifications.filter(n => n.id > this.lastNotificationId);
                    if (newNotifications.length > 0) {
                        this.handleNewNotifications(newNotifications);
                        this.lastNotificationId = Math.max(...data.notifications.map(n => n.id));
                    }
                }
            }
        } catch (error) {
            console.error('خطأ في التحقق من الإشعارات:', error);
        }
    }

    /**
     * معالجة الإشعارات الجديدة
     */
    handleNewNotifications(notifications) {
        notifications.forEach(notification => {
            this.showDesktopNotification(notification);
            this.playNotificationSound();
            this.showInAppNotification(notification);
        });
    }

    /**
     * عرض إشعار سطح المكتب
     */
    showDesktopNotification(notification) {
        if (!this.settings.desktopNotifications || !('Notification' in window) || Notification.permission !== 'granted') {
            return;
        }

        const desktopNotification = new Notification(notification.title, {
            body: notification.message,
            icon: '/assets/img/logo.png',
            badge: '/assets/img/logo.png',
            tag: `notification-${notification.id}`,
            requireInteraction: true
        });

        desktopNotification.onclick = () => {
            window.focus();
            this.markAsRead(notification.id);
            desktopNotification.close();
        };

        // إغلاق تلقائي بعد 5 ثوان
        setTimeout(() => {
            desktopNotification.close();
        }, 5000);
    }

    /**
     * تشغيل صوت الإشعار
     */
    playNotificationSound() {
        if (this.settings.soundEnabled && this.audio) {
            this.audio.currentTime = 0;
            this.audio.play().catch(error => {
                console.warn('فشل في تشغيل صوت الإشعار:', error);
            });
        }
    }

    /**
     * عرض إشعار داخل التطبيق
     */
    showInAppNotification(notification) {
        // إنشاء عنصر الإشعار
        const notificationElement = document.createElement('div');
        notificationElement.className = `alert alert-${notification.color} alert-dismissible fade show notification-toast`;
        notificationElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        notificationElement.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fa ${notification.icon} fa-lg me-2"></i>
                <div>
                    <strong>${notification.title}</strong>
                    <div class="small">${notification.message}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.appendChild(notificationElement);

        // إزالة تلقائية بعد 5 ثوان
        setTimeout(() => {
            if (notificationElement.parentNode) {
                notificationElement.remove();
            }
        }, 5000);
    }

    /**
     * تحديث واجهة الإشعارات
     */
    updateNotificationUI(data) {
        const countElement = document.getElementById('notificationCount');
        const subtextElement = document.getElementById('notificationSubtext');
        
        if (countElement) {
            if (data.count > 0) {
                countElement.textContent = data.count > 99 ? '99+' : data.count;
                countElement.style.display = 'inline-block';
            } else {
                countElement.style.display = 'none';
            }
        }

        if (subtextElement) {
            subtextElement.textContent = data.count > 0 
                ? `لديك ${data.count} إشعار جديد`
                : 'لا توجد إشعارات جديدة';
        }
    }

    /**
     * تحميل قائمة الإشعارات
     */
    async loadNotifications() {
        try {
            const response = await fetch('/notifications/unread');
            if (response.ok) {
                const data = await response.json();
                this.renderNotificationList(data.notifications);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
        }
    }

    /**
     * عرض قائمة الإشعارات
     */
    renderNotificationList(notifications) {
        const listElement = document.getElementById('notificationList');
        if (!listElement) return;

        if (notifications.length === 0) {
            listElement.innerHTML = `
                <div class="text-center p-3">
                    <i class="fa fa-bell-slash fa-2x text-muted mb-2"></i>
                    <p class="text-muted">لا توجد إشعارات جديدة</p>
                </div>
            `;
            return;
        }

        listElement.innerHTML = notifications.map(notification => `
            <a class="d-flex p-3 border-bottom notification-item" href="#" data-id="${notification.id}">
                <div class="notifyimg bg-${notification.color}">
                    <i class="fa ${notification.icon} text-white"></i>
                </div>
                <div class="mr-3 flex-grow-1">
                    <h6 class="notification-label mb-1">${notification.title}</h6>
                    <div class="notification-subtext small">${notification.message}</div>
                    <div class="notification-time small text-muted">${this.formatTime(notification.created_at)}</div>
                </div>
                <div class="mr-auto">
                    <i class="las la-angle-left text-muted"></i>
                </div>
            </a>
        `).join('');
    }

    /**
     * تحديد إشعار كمقروء
     */
    async markAsRead(notificationId) {
        try {
            const response = await fetch(`/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (response.ok) {
                this.checkForNewNotifications();
            }
        } catch (error) {
            console.error('خطأ في تحديد الإشعار كمقروء:', error);
        }
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    async markAllAsRead() {
        try {
            const response = await fetch('/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (response.ok) {
                this.checkForNewNotifications();
                this.loadNotifications();
            }
        } catch (error) {
            console.error('خطأ في تحديد جميع الإشعارات كمقروءة:', error);
        }
    }

    /**
     * تنسيق الوقت
     */
    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays < 7) return `منذ ${diffInDays} يوم`;
        
        return date.toLocaleDateString('ar-SA');
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.setupAudio();
    }

    /**
     * اختبار الصوت
     */
    testSound(soundFile = null, volume = null) {
        const testAudio = new Audio(`/assets/sounds/${soundFile || this.settings.soundFile}`);
        testAudio.volume = (volume || this.settings.soundVolume) / 100;
        testAudio.play().catch(error => {
            console.warn('فشل في تشغيل الصوت التجريبي:', error);
        });
    }

    /**
     * إنشاء إشعار تجريبي
     */
    async createTestNotification() {
        try {
            const response = await fetch('/notifications/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (response.ok) {
                setTimeout(() => {
                    this.checkForNewNotifications();
                }, 1000);
            }
        } catch (error) {
            console.error('خطأ في إنشاء الإشعار التجريبي:', error);
        }
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.notificationSystem = new NotificationSystem();
});

// تصدير للاستخدام العام
window.NotificationSystem = NotificationSystem;
