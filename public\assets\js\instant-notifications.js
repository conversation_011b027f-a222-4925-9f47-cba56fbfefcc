/**
 * نظام الإشعارات الفورية لجميع الأجهزة
 * يعمل بسرعة فائقة عند إضافة عميل جديد
 */

class InstantNotificationSystem {
    constructor() {
        this.isEnabled = true;
        this.lastClientId = 0;
        this.checkInterval = 5000; // 5 ثوان للتحقق السريع
        this.init();
    }

    init() {
        this.requestNotificationPermission();
        this.startRealTimeCheck();
        this.setupEventListeners();
    }

    /**
     * طلب إذن الإشعارات
     */
    async requestNotificationPermission() {
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    this.showWelcomeNotification();
                }
            }
        }
    }

    /**
     * إشعار ترحيبي
     */
    showWelcomeNotification() {
        new Notification('🎉 تم تفعيل الإشعارات!', {
            body: 'ستحصل على إشعارات فورية عند إضافة عملاء جدد',
            icon: '/favicon.ico',
            tag: 'welcome'
        });
    }

    /**
     * بدء التحقق الفوري
     */
    startRealTimeCheck() {
        // تحديد آخر ID للعميل
        this.getLastClientId();
        
        // التحقق كل 5 ثوان
        setInterval(() => {
            this.checkForNewClients();
        }, this.checkInterval);
    }

    /**
     * الحصول على آخر ID للعميل
     */
    async getLastClientId() {
        try {
            const response = await fetch('/api/last-client-id');
            if (response.ok) {
                const data = await response.json();
                this.lastClientId = data.last_id || 0;
            }
        } catch (error) {
            console.warn('فشل في الحصول على آخر ID للعميل:', error);
        }
    }

    /**
     * التحقق من العملاء الجدد
     */
    async checkForNewClients() {
        if (!this.isEnabled) return;

        try {
            const response = await fetch(`/api/new-clients-since/${this.lastClientId}`);
            if (response.ok) {
                const data = await response.json();
                
                if (data.clients && data.clients.length > 0) {
                    data.clients.forEach(client => {
                        this.showInstantNotification(client);
                        this.playNotificationSound();
                        this.showInPageAlert(client);
                    });
                    
                    // تحديث آخر ID
                    this.lastClientId = Math.max(...data.clients.map(c => c.id));
                    
                    // تحديث قائمة العملاء الجدد إذا كانت موجودة
                    if (window.newClientsManager) {
                        window.newClientsManager.loadNewClients();
                    }
                }
            }
        } catch (error) {
            console.warn('خطأ في التحقق من العملاء الجدد:', error);
        }
    }

    /**
     * إشعار فوري
     */
    showInstantNotification(client) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification('👤 عميل جديد!', {
                body: `تم إضافة: ${client.name}${client.phone ? ' - ' + client.phone : ''}`,
                icon: '/favicon.ico',
                tag: `client-${client.id}`,
                requireInteraction: true, // يبقى الإشعار حتى يتفاعل المستخدم
                actions: [
                    {
                        action: 'view',
                        title: 'عرض العميل'
                    },
                    {
                        action: 'dismiss',
                        title: 'إغلاق'
                    }
                ]
            });

            // التعامل مع النقر على الإشعار
            notification.onclick = () => {
                window.focus();
                window.location.href = `/clients/${client.id}`;
                notification.close();
            };

            // إغلاق تلقائي بعد 10 ثوان
            setTimeout(() => {
                notification.close();
            }, 10000);
        }
    }

    /**
     * تشغيل صوت الإشعار
     */
    playNotificationSound() {
        try {
            // صوت مميز للعملاء الجدد
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // نغمة أولى
            this.playTone(audioContext, 800, 0.1, 0.3);
            
            // نغمة ثانية بعد 200ms
            setTimeout(() => {
                this.playTone(audioContext, 1000, 0.1, 0.2);
            }, 200);
            
            // نغمة ثالثة بعد 400ms
            setTimeout(() => {
                this.playTone(audioContext, 1200, 0.1, 0.2);
            }, 400);
            
        } catch (error) {
            console.warn('فشل في تشغيل الصوت:', error);
        }
    }

    /**
     * تشغيل نغمة واحدة
     */
    playTone(audioContext, frequency, duration, volume) {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = frequency;
        oscillator.type = "sine";
        
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
    }

    /**
     * إشعار داخل الصفحة
     */
    showInPageAlert(client) {
        // إنشاء إشعار في أعلى الصفحة
        const alertDiv = document.createElement('div');
        alertDiv.className = 'instant-notification-alert';
        alertDiv.innerHTML = `
            <div class="instant-notification-content">
                <div class="instant-notification-icon">
                    <i class="fa fa-user-plus"></i>
                </div>
                <div class="instant-notification-text">
                    <strong>عميل جديد!</strong>
                    <span>${client.name}${client.phone ? ' - ' + client.phone : ''}</span>
                </div>
                <div class="instant-notification-actions">
                    <button onclick="window.location.href='/clients/${client.id}'" class="btn btn-sm btn-success">
                        عرض
                    </button>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" class="btn btn-sm btn-secondary">
                        إغلاق
                    </button>
                </div>
            </div>
        `;

        // إضافة الإشعار للصفحة
        document.body.appendChild(alertDiv);

        // تأثير الظهور
        setTimeout(() => {
            alertDiv.classList.add('show');
        }, 100);

        // إزالة تلقائية بعد 15 ثانية
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.classList.remove('show');
                setTimeout(() => {
                    alertDiv.remove();
                }, 300);
            }
        }, 15000);
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تفعيل/إلغاء تفعيل الإشعارات
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'N') {
                this.toggleNotifications();
            }
        });
    }

    /**
     * تفعيل/إلغاء تفعيل الإشعارات
     */
    toggleNotifications() {
        this.isEnabled = !this.isEnabled;
        const status = this.isEnabled ? 'مفعلة' : 'معطلة';
        
        this.showInPageAlert({
            name: `الإشعارات الفورية ${status}`,
            phone: '',
            id: 0
        });
    }

    /**
     * إضافة عميل جديد فوراً (للاستخدام من صفحات أخرى)
     */
    addClientInstantly(clientData) {
        this.showInstantNotification(clientData);
        this.playNotificationSound();
        this.showInPageAlert(clientData);
        
        // تحديث آخر ID
        if (clientData.id > this.lastClientId) {
            this.lastClientId = clientData.id;
        }
    }
}

// تنسيق CSS للإشعارات
const instantNotificationCSS = `
.instant-notification-alert {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    transform: translateX(400px);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    min-width: 350px;
    max-width: 450px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.instant-notification-alert.show {
    transform: translateX(0);
}

.instant-notification-content {
    display: flex;
    align-items: center;
    padding: 16px;
    gap: 12px;
}

.instant-notification-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.instant-notification-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.instant-notification-text strong {
    font-size: 14px;
    font-weight: 600;
}

.instant-notification-text span {
    font-size: 13px;
    opacity: 0.9;
}

.instant-notification-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.instant-notification-actions .btn {
    padding: 4px 12px;
    font-size: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    transition: all 0.2s;
}

.instant-notification-actions .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

@media (max-width: 576px) {
    .instant-notification-alert {
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
        transform: translateY(-100px);
    }
    
    .instant-notification-alert.show {
        transform: translateY(0);
    }
    
    .instant-notification-content {
        padding: 12px;
        gap: 8px;
    }
    
    .instant-notification-actions {
        flex-direction: column;
        gap: 4px;
    }
}
`;

// إضافة CSS للصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = instantNotificationCSS;
document.head.appendChild(styleSheet);

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.instantNotificationSystem = new InstantNotificationSystem();
});

// تصدير للاستخدام العام
window.InstantNotificationSystem = InstantNotificationSystem;
