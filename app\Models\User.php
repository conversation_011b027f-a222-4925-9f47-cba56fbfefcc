<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;


class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'role',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    // public function isSuper()
    // {

    // return $this->role == 'super';
    // }

    // public function isAdmin()
    //  {
    //     return $this->role == 'admin';
    //   }

    //   public function isUser()
    //    {
    //     return $this->role == 'user';
    //   }

    //   public function isSecretary()
    //   {
    //    return $this->role == 'secretary';
    //  }

    //   public function isServiceProvider()
    //   {
    //     return $this->role == 'serviceProvider';
    //   }

    /**
     * العلاقة مع الإشعارات
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * العلاقة مع إعدادات الإشعارات
     */
    public function notificationSettings()
    {
        return $this->hasOne(NotificationSetting::class);
    }

    /**
     * العلاقة مع سجلات النسخ الاحتياطي
     */
    public function backupLogs()
    {
        return $this->hasMany(BackupLog::class, 'created_by');
    }

}
