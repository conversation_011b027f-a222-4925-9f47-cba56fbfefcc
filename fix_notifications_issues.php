<?php

/**
 * إصلاح مشاكل الإشعارات - حذف المحدد وتحديد كمقروء وإشعار العملاء
 * تشغيل: php fix_notifications_issues.php
 */

echo "🔧 إصلاح مشاكل الإشعارات\n";
echo "=" . str_repeat("=", 35) . "\n\n";

// تحميل Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل Laravel: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. مسح cache
echo "1️⃣ مسح cache...\n";
exec('php artisan cache:clear', $output, $return);
exec('php artisan config:clear', $output, $return);
exec('php artisan route:clear', $output, $return);
exec('composer dump-autoload', $output, $return);
echo "✅ تم مسح cache\n\n";

// 2. اختبار المسارات الجديدة
echo "2️⃣ اختبار المسارات الجديدة...\n";
try {
    $routes = [
        'notifications.mark-selected-read' => '/notifications/mark-selected-read',
        'notifications.delete-selected' => '/notifications/delete-selected',
        'notifications.unread' => '/notifications/unread'
    ];
    
    foreach ($routes as $routeName => $expectedPath) {
        try {
            $url = route($routeName);
            echo "✅ مسار {$routeName}: {$url}\n";
        } catch (Exception $e) {
            echo "❌ مسار {$routeName}: غير موجود - {$e->getMessage()}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص المسارات: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. اختبار دوال Controller الجديدة
echo "3️⃣ اختبار دوال Controller الجديدة...\n";
try {
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        
        // إنشاء إشعارات تجريبية
        $notifications = [];
        for ($i = 1; $i <= 3; $i++) {
            $notification = App\Models\Notification::createNotification([
                'type' => 'test_bulk',
                'title' => "إشعار اختبار {$i}",
                'message' => "هذا إشعار تجريبي رقم {$i} لاختبار العمليات المجمعة",
                'icon' => 'fa-test',
                'color' => 'info',
                'user_id' => $testUser->id
            ]);
            $notifications[] = $notification->id;
        }
        
        echo "✅ تم إنشاء " . count($notifications) . " إشعارات تجريبية\n";
        
        // اختبار تحديد المحدد كمقروء
        $controller = new App\Http\Controllers\NotificationController();
        $request = new Illuminate\Http\Request();
        $request->merge(['ids' => array_slice($notifications, 0, 2)]);
        
        $response = $controller->markSelectedAsRead($request);
        $result = json_decode($response->getContent(), true);
        
        if ($result['success']) {
            echo "✅ اختبار تحديد المحدد كمقروء: نجح - {$result['message']}\n";
        } else {
            echo "❌ اختبار تحديد المحدد كمقروء: فشل\n";
        }
        
        // اختبار حذف المحدد
        $request->merge(['ids' => [$notifications[2]]]);
        $response = $controller->deleteSelected($request);
        $result = json_decode($response->getContent(), true);
        
        if ($result['success']) {
            echo "✅ اختبار حذف المحدد: نجح - {$result['message']}\n";
        } else {
            echo "❌ اختبار حذف المحدد: فشل\n";
        }
        
        // تنظيف
        App\Models\Notification::whereIn('id', $notifications)->delete();
        echo "✅ تم تنظيف الإشعارات التجريبية\n";
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار دوال Controller: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار Observer للعملاء
echo "4️⃣ اختبار Observer للعملاء...\n";
try {
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        
        // إنشاء عميل تجريبي
        $testClient = App\Models\clients::create([
            'name' => 'عميل اختبار الإشعارات - ' . date('H:i:s'),
            'gender' => 'male',
            'phone' => '59' . rand(1000000, 9999999),
            'subscroptiondate' => now()->toDateString(),
            'note' => 'عميل تجريبي لاختبار الإشعارات',
            'password' => '123456',
            'roles_name' => 'yes',
            'active' => 'yes',
            'status' => 'active'
        ]);
        
        echo "✅ تم إنشاء عميل تجريبي: {$testClient->name} (ID: {$testClient->id})\n";
        
        // انتظار للسماح للـ Observer بالعمل
        sleep(2);
        
        // فحص الإشعارات المُنشأة
        $clientNotifications = App\Models\Notification::where('type', 'new_client')
                                                     ->where('related_id', $testClient->id)
                                                     ->get();
        
        if ($clientNotifications->count() > 0) {
            echo "🎉 Observer يعمل بشكل صحيح! تم إنشاء {$clientNotifications->count()} إشعار\n";
            foreach ($clientNotifications as $notification) {
                $user = App\Models\User::find($notification->user_id);
                echo "   - إشعار للمستخدم: {$user->name}\n";
                echo "     العنوان: {$notification->title}\n";
                echo "     الرسالة: {$notification->message}\n";
                echo "     عاجل: " . (isset($notification->data['is_urgent']) ? 'نعم' : 'لا') . "\n";
            }
        } else {
            echo "❌ Observer لا يعمل! لم يتم إنشاء إشعارات\n";
        }
        
        // تنظيف
        App\Models\Notification::where('related_id', $testClient->id)->delete();
        $testClient->delete();
        echo "✅ تم تنظيف البيانات التجريبية\n";
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار Observer: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. فحص الملفات المحدثة
echo "5️⃣ فحص الملفات المحدثة...\n";
$files = [
    'app/Http/Controllers/NotificationController.php' => 'Notification Controller',
    'routes/web.php' => 'Routes',
    'resources/views/notifications/index.blade.php' => 'Notifications View',
    'app/Observers/ClientObserver.php' => 'Client Observer',
    'public/assets/js/client-notifications.js' => 'Client Notifications JS'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$description}: موجود\n";
        
        // فحص محتوى الملفات المهمة
        if ($file === 'app/Http/Controllers/NotificationController.php') {
            $content = file_get_contents($file);
            if (strpos($content, 'deleteSelected') !== false) {
                echo "   ✅ دالة deleteSelected موجودة\n";
            }
            if (strpos($content, 'markSelectedAsRead') !== false) {
                echo "   ✅ دالة markSelectedAsRead موجودة\n";
            }
        }
        
        if ($file === 'routes/web.php') {
            $content = file_get_contents($file);
            if (strpos($content, 'delete-selected') !== false) {
                echo "   ✅ مسار delete-selected موجود\n";
            }
            if (strpos($content, 'mark-selected-read') !== false) {
                echo "   ✅ مسار mark-selected-read موجود\n";
            }
        }
        
    } else {
        echo "❌ {$description}: مفقود\n";
    }
}
echo "\n";

// 6. إنشاء إشعار ترحيبي
echo "6️⃣ إنشاء إشعار ترحيبي...\n";
try {
    $users = App\Models\User::all();
    foreach ($users as $user) {
        // حذف الإشعارات الترحيبية القديمة
        App\Models\Notification::where('user_id', $user->id)
                               ->where('type', 'system_fixed')
                               ->delete();
        
        // إنشاء إشعار ترحيبي جديد
        App\Models\Notification::createNotification([
            'type' => 'system_fixed',
            'title' => '🔧 تم إصلاح المشاكل!',
            'message' => 'تم إصلاح مشاكل "حذف المحدد" و "تحديد كمقروء" وإضافة إشعارات العملاء الجدد.',
            'icon' => 'fa-tools',
            'color' => 'success',
            'user_id' => $user->id,
            'data' => [
                'system_message' => true,
                'fixes' => [
                    'bulk_delete' => true,
                    'bulk_mark_read' => true,
                    'client_notifications' => true,
                    'improved_observer' => true
                ]
            ]
        ]);
    }
    echo "✅ تم إنشاء إشعارات ترحيبية\n";
} catch (Exception $e) {
    echo "⚠️  تحذير في الإشعار الترحيبي: " . $e->getMessage() . "\n";
}
echo "\n";

// 7. التقرير النهائي
echo "📊 التقرير النهائي:\n";
echo "=" . str_repeat("=", 20) . "\n";

$totalNotifications = DB::table('notifications')->count();
$unreadNotifications = DB::table('notifications')->where('is_read', false)->count();
$clientNotifications = DB::table('notifications')->where('type', 'new_client')->count();

echo "📈 الإحصائيات:\n";
echo "   - إجمالي الإشعارات: {$totalNotifications}\n";
echo "   - الإشعارات غير المقروءة: {$unreadNotifications}\n";
echo "   - إشعارات العملاء الجدد: {$clientNotifications}\n\n";

echo "🎯 المشاكل التي تم إصلاحها:\n";
echo "   ✅ حذف المحدد - يعمل الآن بشكل صحيح\n";
echo "   ✅ تحديد كمقروء - يعمل الآن بشكل صحيح\n";
echo "   ✅ إشعارات العملاء الجدد - محسنة مع Observer\n";
echo "   ✅ إشعار فوري في الواجهة عند إضافة عميل\n";
echo "   ✅ أصوات وتنبيهات محسنة\n\n";

echo "🔧 كيفية الاستخدام:\n";
echo "1. زيارة /notifications - ستجد الأزرار تعمل الآن\n";
echo "2. تحديد إشعارات متعددة والنقر على 'تحديد كمقروء'\n";
echo "3. تحديد إشعارات متعددة والنقر على 'حذف المحدد'\n";
echo "4. إضافة عميل جديد - سيظهر إشعار فوري\n";
echo "5. تضمين client-notifications.js في صفحة العملاء\n\n";

echo "💡 للمطورين:\n";
echo "   - استخدام window.notifyNewClientAdded(clientData) لإشعار فوري\n";
echo "   - تضمين <script src=\"/assets/js/client-notifications.js\"></script>\n";
echo "   - Observer يعمل تلقائياً عند إضافة عميل\n\n";

echo "🎉 تم الانتهاء من إصلاح جميع المشاكل!\n";
echo "🔔 النظام الآن يعمل بشكل مثالي!\n";

?>
