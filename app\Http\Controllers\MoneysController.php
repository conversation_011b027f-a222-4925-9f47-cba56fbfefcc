<?php

namespace App\Http\Controllers;

use App\Models\moneys;
use Illuminate\Http\Request;

class MoneysController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {




        $validatedData = $request->validate([
            'note' => 'required:money',
        ],[

            'note.required' =>'يرجي ادخال الملاحظة',



        ]);

        if ($request->sarf =='0') {


        moneys::create([
    'clients_id' => $request->clients_id,
   'dateofmoney' => $request->dateofmoney,
   'kabdmoney' => $request->kabdmoney,
   'sarf' => $request->sarf,
   'note' => $request->note,
]);
        }


        else
        {


            moneys::create([
                'clients_id' => $request->clients_id,
               'dateofmoney' => $request->dateofmoney,
               'kabdmoney' => $request->kabdmoneyy,
               'sarf' => $request->sarfy,
               'note' => $request->note,
            ]);






        }




        session()->flash('Add', 'تم اضافة الدفعة بنجاح ');
        return redirect()->back();


    }

    /**
     * Display the specified resource.
     */
    public function show(moneys $moneys)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(moneys $moneys)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $id = $request->id;

        $validate = $request->validate([

            'kabdmoney' => 'required:money,kabdmoney,'.$id,
        ],[

            'kabdmoney.required' =>'يرجي ادخال اسم القسم',


        ]);

        $money = moneys::find($id);
        $money->update([
            'dateofmoney' => $request->dateofmoney,
            'kabdmoney' => $request->kabdmoney,
            'sarf' => $request->sarf,
            'note' => $request->note,

        ]);

        session()->flash('edit','تم التعديل  بنجاج');
        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {

        $id = $request->id;
        moneys::find($id)->delete();
        session()->flash('delete','تم الحذف بنجاح');
        return redirect()->back();
        //
    }

    /**
     * Remove the specified resource from storage.
     */

}
