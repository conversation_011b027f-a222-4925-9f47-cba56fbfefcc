<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class subscriptions extends Model
{
    use HasFactory;

    protected $table = "subscriptions";
    protected $fillable =
        [
            'subscriptionsname',
            'startdatesubscriptions',
            'timeenddatesubscriptions',
            'clients_id',
            'subscriptions_id',
            'startdateofcustomerchallenges',
            'endtdateofcustomerchallenges',


        ];
    public $timestamps = true;


##########

public function Hed_estpsmsmssclint()
{
    return $this->belongsTo('App\Models\clientschallenges','id');
}

#########################



}
