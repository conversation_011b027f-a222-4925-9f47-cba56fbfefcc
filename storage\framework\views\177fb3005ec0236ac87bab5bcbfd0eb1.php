<?php $__env->startSection('title'); ?>
    المواعيد ..
    <?php
        $infocompanies = DB::table('infocompanies')->get();
    ?>
    <?php echo e($infocompanies->implode('titleofcompany')); ?>


<?php $__env->stopSection(); ?>
<?php $__env->startSection('css'); ?>

    <!-- Internal Data table css -->
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/prism/prism.css')); ?>" rel="stylesheet">
    <!---Internal Owl Carousel css-->
    <link href="<?php echo e(URL::asset('assets/plugins/owl-carousel/owl.carousel.css')); ?>" rel="stylesheet">
    <!---Internal  Multislider css-->
    <link href="<?php echo e(URL::asset('assets/plugins/multislider/multislider.css')); ?>" rel="stylesheet">
    <!--- Select2 css -->
    <link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">


    <link href="<?php echo e(URL::asset('assets/plugins/select2/css/bootstrap.min.css')); ?>" rel="stylesheet">



<?php $__env->stopSection(); ?>
<?php $__env->startSection('page-header'); ?>
    <!-- breadcrumb -->
    <div class="breadcrumb-header justify-content-between">
        <div class="my-auto">
            <div class="d-flex">
                <h4 class="content-title mb-0 my-auto">الزبائن</h4><span class="text-muted mt-1 tx-13 mr-2 mb-0">/ قائمة
                    المواعيد</span>
            </div>
        </div>


        <!--
                                                                     <div class="d-flex my-xl-auto right-content">
                                                                      <div class="pr-1 mb-3 mb-xl-0">
                                                                       <button type="button" class="btn btn-info btn-icon ml-2"><i class="mdi mdi-filter-variant"></i></button>
                                                                      </div>
                                                                      <div class="pr-1 mb-3 mb-xl-0">
                                                                       <button type="button" class="btn btn-danger btn-icon ml-2"><i class="mdi mdi-star"></i></button>
                                                                      </div>
                                                                      <div class="pr-1 mb-3 mb-xl-0">
                                                                       <button type="button" class="btn btn-warning  btn-icon ml-2"><i class="mdi mdi-refresh"></i></button>
                                                                      </div>
                                                                      <div class="mb-3 mb-xl-0">
                                                                       <div class="btn-group dropdown">
                                                                        <button type="button" class="btn btn-primary">14 Aug 2019</button>
                                                                        <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" id="dropdownMenuDate" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                        <span class="sr-only">Toggle Dropdown</span>
                                                                        </button>
                                                                        <div class="dropdown-menu dropdown-menu-left" aria-labelledby="dropdownMenuDate" data-x-placement="bottom-end">
                                                                         <a class="dropdown-item" href="#">2015</a>
                                                                         <a class="dropdown-item" href="#">2016</a>
                                                                         <a class="dropdown-item" href="#">2017</a>
                                                                         <a class="dropdown-item" href="#">2018</a>
                                                                        </div>
                                                                       </div>
                                                                      </div>
                                                                     </div>

                                                                                    -->
    </div>
    <!-- breadcrumb -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>


    <!-- row -->




    <!-- row opened -->
    <div class="row row-sm">

        <!--/div-->

        <!--div-->
        <div class="col-xl-12">


            <?php if($errors->any()): ?>
                <div class="alert alert-danger">
                    <ul>
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if(session()->has('Add')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <strong><?php echo e(session()->get('Add')); ?></strong>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <?php if(session()->has('delete')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <strong><?php echo e(session()->get('delete')); ?></strong>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <?php if(session()->has('edit')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <strong><?php echo e(session()->get('edit')); ?></strong>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>



            <!-- row opened -->
            <div class="row row-sm">

                

                <?php

                    // $date1 = new DateTime($infocompanies->implode('dateofinsertofactive'));
                    // $date2 = new DateTime($infocompanies->implode('dateofendofactive'));
                    // $interval = $date2->diff($date1);

                    $fdate = $infocompanies->implode('dateofinsertofactive');
                    $tdate = $infocompanies->implode('dateofendofactive');
                    $datetime1 = strtotime($fdate); // convert to timestamps
                    $datetime2 = strtotime($tdate); // convert to timestamps
                    $days = (int) (($datetime2 - $datetime1) / 86400); // will give the difference in days , 86400 is the timestamp difference of a day

                ?>
                <?php

if ($days < 3) {
// exit program normally
//Redirect::url("/logout")
echo "<script>alert('  .... ضروري جدا  ....لقد بقي من وقت التفعيل  (  $days  )   أيام')</script>";

Auth::logout();
Session::flush();

echo "<script> window.location='login'</script>";

return Redirect::to('login');
return Redirect::to('home');
exit();
}

if ($days < 15) {
// exit program normally
//Redirect::url("/logout")
echo "<script>alert(' لقد بقي من وقت التفعيل (  $days  ) أيام وعلى ذلك الرجاء التجديد قبل وصول البرنامج ل 3 أيام')</script>";




?>



                <div class="alert alert-danger">
                    <strong> الرجاء الحذر! </strong> لقد بقي من وقت التفعيل ( <?php echo e($days); ?> ) أيام

                    وعلى ذلك الرجاء التجديد قبل وصول البرنامج ل 3 ايام
                </div>


                <?php
}
?>



                



                <div class="col-xl-12">
                    <div class="card">
                        <div class="card-header pb-0">
                            <div class="d-flex justify-content-between">
                                <h4 class="card-title mg-b-0">قائمة المواعيد <img alt="قائمة المواعيد"
                                        title="قائمة المواعيد" src="<?php echo e(URL::asset('assets/img/media/jalal/planner.png')); ?>"
                                        class=""></h4>
                                <i class="mdi mdi-dots-horizontal text-gray"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table" style="max-width:500px" id="example76">
                                    <thead>


                                        <tr style="text-align: center">
                                            <th scope="col"></th>
                                            <th scope="col">المعرف</th>
                                            <th scope="col">اسم الزبون</th>
                                            <th scope="col">الملاحظات</th>
                                            <th scope="col">تاريخ الزيارة الحالية</th>
                                            <th scope="col">عدد الايام</th>
                                            <th scope="col">تاريخ الزيارة القادمة</th>
                                            <th scope="col">بقي للموعد</th>
                                            <th scope="col">رسالة وتس</th>


                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $i = 0; ?>
                                        <?php $__currentLoopData = $visits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $x): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php $i++; ?>






                                            <tr style="text-align: center">
                                                <td>

                                                    <a class="modal-effect btn btn-sm btn-info" data-effect="effect-scale"
                                                        data-id="<?php echo e($x->id); ?>"
                                                        data-clients_id="<?php echo e($x->clients->name); ?>"
                                                        data-dateofvisit="<?php echo e($x->dateofvisit); ?>"
                                                        data-nextvisit="<?php echo e($x->nextvisit); ?>"
                                                        data-note="<?php echo e($x->note); ?>" data-toggle="modal"
                                                        href="#exampleModal2" title="تعديل"><i
                                                            class="las la-pen"></i></a>




                                                    <a class="modal-effect btn btn-sm btn-danger"
                                                        data-effect="effect-scale" data-id="<?php echo e($x->id); ?>"
                                                        data-clients_id="<?php echo e($x->clients->name); ?>"
                                                        data-dateofvisit="<?php echo e($x->dateofvisit); ?>"
                                                        data-nextvisit="<?php echo e($x->nextvisit); ?>"
                                                        data-note="<?php echo e($x->note); ?>" data-toggle="modal"
                                                        href="#modaldemo9" title="حذف"><i
                                                            class="las la-trash"></i></a>

                                                </td>
                                                <td><?php echo e($i); ?></td>
                                                <td><?php echo e($x->clients->name); ?></td>
                                                <td><?php echo e($x->note); ?></td>
                                                <td><?php echo e($x->dateofvisit); ?></td>
                                                <td><?php echo e($x->nextvisit); ?></td>


                                                <td>



                                                    <?php

                                                    $timeago = date('Y-m-d', strtotime(now()->parse($x->dateofvisit)->addDays($x->nextvisit)));
                                                    echo $timeago;
                                                    echo '<br>';

                                                    ?>
                                                </td>
                                                <td>
                                                    <?php

                                                    $date1 = new DateTime('today');

                                                    $date2 = new DateTime($timeago);

                                                    $interval = $date1->diff($date2)->format('%r%a');

                                                    if ($interval < 0) {
                                                        echo "<p style='color: rgb(204, 191, 15);'>ذهب موعده </p>";
                                                    } elseif ($interval == 0) {
                                                        echo "<p style='color: red;'>اليوم موعده </p>";
                                                    }

                                                    if ($interval >= 4) {
                                                        echo "<p style='color: green;'>بقي    $interval   ايام </p>";
                                                    } elseif ($interval < 4 && $interval > 0) {
                                                        echo "<p style='color: rgb(144, 37, 148);'>بقي    $interval    ايام </p>";
                                                    }
                                                    ?>
                                                </td>

                                                <td>
                                                    <a target="_blank"
                                                        href="https://wa.me/<?php echo e($x->clients->phone); ?>?text= مرحبا بك <?php echo e($x->clients->name); ?> في مركز سيا قسم التغذية لقد بقي لموعدك <?php echo $interval; ?> يوم">
                                                        <img src="<?php echo e(URL::asset('assets/img/media/jalal/WhatsApp_icon.png')); ?>"
                                                            alt="رسالة وتس اب" title="رسالة وتس اب" ">
                                                                                                      </a>

                                                                                                </td>


                                                                                                </tr>
     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>


                                </table>

                            </div>
                        </div>
                    </div>
                </div>
                <!--/div-->

            </div>
            <!-- /row -->


            <!-- Basic modal -->
            <div class="modal" id="modaldemo8">
                <div class="modal-dialog" role="document">
                    <div class="modal-content modal-content-demo">
                        <div class="modal-header">
                            <h6 class="modal-title">إضافة قسم</h6><button aria-label="Close" class="close"
                                data-dismiss="modal" type="button"><span aria-hidden="true">&times;</span></button>
                        </div>
                        <div class="modal-body">
                            <form action="<?php echo e(route('visits.store')); ?>" method="post">
                                <?php echo e(csrf_field()); ?>

                                <div class="form-group">
                                    <label for="exampleInputEmail1">السؤال</label>
                                    <input type="text" class="form-control" id="question" name="question">
                                </div>




                                <div class="modal-footer">
                                    <button type="submit" class="btn btn-success">تاكيد</button>
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                                </div>
                            </form>

                        </div>

                    </div>

                </div>
                <!-- End Basic modal -->

            </div>

        </div>

        <!-- edit -->
        <div class="modal fade" id="exampleModal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModal2">تعديل الموعد</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form action='visits/update' method="post" autocomplete="off">
                        <?php echo e(method_field('patch')); ?>

                        <?php echo e(csrf_field()); ?>

                        <div class="modal-body">

                            <div class="form-group">
                                <label for="title">اسم الشخص :</label>

                                <input type="hidden" class="form-control" name="id" id="id"
                                    value="">
                                <input type="text" class="form-control" name="clients_id" id="clients_id" readonly>
                                <label for="title">تاريخ الزيارة الحالية :</label>
                                <input type="date" class="form-control" name="dateofvisit" id="dateofvisit">

                                <label for="title"> الملاحظات :</label>
                                <input type="text" class="form-control" name="note" id="note">

                                <label for="title">عدد الايام :</label>

                                <input type="number" class="form-control" name="nextvisit" id="nextvisit">

                                <label for="title">تاريخ الزيارة القادمة :</label>


                                <input type="text" class="form-control" name="fdate" id="fdate">




                            </div>


                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                        </div>
                    </form>
                </div>
            </div>

        </div>
        <!-- delete -->
        <div class="modal fade" id="modaldemo9" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">حذف الموعد</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>


                    <form action="visits/destroy" method="post">
                        <?php echo e(method_field('delete')); ?>

                        <?php echo e(csrf_field()); ?>

                        <div class="modal-body">
                            <p>هل انت متاكد من عملية الحذف ؟</p><br>
                            <input type="hidden" name="id" id="id" value="">
                            <input class="form-control" name="clients_id" id="clients_id" type="text" readonly>

                            <label for="title">عدد الايام :</label>
                            <input class="form-control" name="nextvisit" id="nextvisit" type="text" readonly>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">الغاء</button>
                            <button type="submit" class="btn btn-danger">تاكيد</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>


    </div>
    <!-- row closed -->

    <!-- Container closed -->

    </div>
    <!-- main-content closed -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <!-- Internal Data tables -->
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jszip.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/pdfmake.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/vfs_fonts1.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.print.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/bootstrap.bundle.min.js')); ?>"></script>
    <!--Internal  Datatable js -->
    <script src="<?php echo e(URL::asset('assets/js/table-data.js')); ?>"></script>


    <script src="/p/pdfmake.js"></script>
    <script src="/p/vfs_fonts.js"></script>


    <!-- Internal Prism js-->
    <script src="<?php echo e(URL::asset('assets/plugins/prism/prism.js')); ?>"></script>
    <!--Internal  Datepicker js -->
    <script src="<?php echo e(URL::asset('assets/plugins/jquery-ui/ui/widgets/datepicker.js')); ?>"></script>
    <!-- Internal Select2 js-->
    <script src="<?php echo e(URL::asset('assets/plugins/select2/js/select2.min.js')); ?>"></script>
    <!-- Internal Modal js-->
    <script src="<?php echo e(URL::asset('assets/js/modal.js')); ?>"></script>



    <script>
        $('#exampleModal2').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var clients_id = button.data('clients_id')
            var dateofvisit = button.data('dateofvisit')
            var nextvisit = button.data('nextvisit')
            var note = button.data('note')

            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #clients_id').val(clients_id);
            modal.find('.modal-body #dateofvisit').val(dateofvisit);
            modal.find('.modal-body #nextvisit').val(nextvisit);
            modal.find('.modal-body #note').val(note);
        })


        $('#modaldemo9').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var clients_id = button.data('clients_id')
            var nextvisit = button.data('nextvisit')

            var modal = $(this)

            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #clients_id').val(clients_id);
            modal.find('.modal-body #nextvisit').val(nextvisit);

        })
    </script>








    <script>
        $('#example76').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>




    <script>
        $("#nextvisit").change(function() {
            var staItems = $('#dateofvisit').val().split("-");
            var date = new Date(Number(staItems[0]), Number(staItems[1]) - 1, Number(staItems[2]));
            date.setDate(date.getDate() + parseInt($(this).val()));
            $('#fdate').val(date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear())




        });
    </script>



    <script type="text/javascript">
        $("button").click(function() {

            var fired_button = $("#fdate").val();
            $('#fdate').val('  ');

        });
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nc\resources\views/visits/visits.blade.php ENDPATH**/ ?>