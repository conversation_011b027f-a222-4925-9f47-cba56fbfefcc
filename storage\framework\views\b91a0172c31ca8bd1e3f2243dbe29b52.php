<?php $__env->startSection('title'); ?>
الإشعارات
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<style>
.notification-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.notification-card.unread {
    border-left: 4px solid #007bff;
    background: linear-gradient(90deg, rgba(0,123,255,0.05) 0%, rgba(255,255,255,1) 10%);
}

.notification-card.read {
    opacity: 0.8;
}

.notification-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.notification-time {
    font-size: 12px;
    color: #6c757d;
}

.notification-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-card:hover .notification-actions {
    opacity: 1;
}

.filter-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.filter-tab {
    padding: 10px 20px;
    border: none;
    background: none;
    color: #6c757d;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.filter-tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.bulk-actions {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: none;
}

.bulk-actions.show {
    display: block;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-header'); ?>
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">الإشعارات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ جميع الإشعارات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="mb-3 mb-xl-0">
            <a href="<?php echo e(route('notifications.settings')); ?>" class="btn btn-secondary">
                <i class="fa fa-cog me-2"></i>
                الإعدادات
            </a>
            <button class="btn btn-primary" id="testNotificationBtn">
                <i class="fa fa-flask me-2"></i>
                إشعار تجريبي
            </button>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- إحصائيات الإشعارات -->
    <div class="col-lg-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0"><?php echo e($notifications->total()); ?></h3>
                    <p class="mb-0">إجمالي الإشعارات</p>
                </div>
                <i class="fa fa-bell fa-2x opacity-50"></i>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><?php echo e($notifications->where('is_read', false)->count()); ?></h3>
                        <p class="mb-0">غير مقروءة</p>
                    </div>
                    <i class="fa fa-envelope fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><?php echo e($notifications->where('is_read', true)->count()); ?></h3>
                        <p class="mb-0">مقروءة</p>
                    </div>
                    <i class="fa fa-envelope-open fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><?php echo e($notifications->where('created_at', '>=', now()->subDay())->count()); ?></h3>
                        <p class="mb-0">اليوم</p>
                    </div>
                    <i class="fa fa-calendar-day fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fa fa-list me-2"></i>
                    قائمة الإشعارات
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" id="selectAllBtn">
                        <i class="fa fa-check-square me-1"></i>
                        تحديد الكل
                    </button>
                    <button class="btn btn-sm btn-outline-success" id="markSelectedReadBtn">
                        <i class="fa fa-eye me-1"></i>
                        تحديد كمقروء
                    </button>
                    <button class="btn btn-sm btn-outline-danger" id="deleteSelectedBtn">
                        <i class="fa fa-trash me-1"></i>
                        حذف المحدد
                    </button>
                </div>
            </div>

            <!-- تبويبات التصفية -->
            <div class="filter-tabs px-3 pt-3">
                <button class="filter-tab active" data-filter="all">
                    <i class="fa fa-list me-1"></i>
                    الكل (<?php echo e($notifications->total()); ?>)
                </button>
                <button class="filter-tab" data-filter="unread">
                    <i class="fa fa-envelope me-1"></i>
                    غير مقروءة (<?php echo e($notifications->where('is_read', false)->count()); ?>)
                </button>
                <button class="filter-tab" data-filter="read">
                    <i class="fa fa-envelope-open me-1"></i>
                    مقروءة (<?php echo e($notifications->where('is_read', true)->count()); ?>)
                </button>
                <button class="filter-tab" data-filter="today">
                    <i class="fa fa-calendar-day me-1"></i>
                    اليوم (<?php echo e($notifications->where('created_at', '>=', now()->subDay())->count()); ?>)
                </button>
            </div>

            <div class="card-body">
                <?php if($notifications->count() > 0): ?>
                    <div id="notificationsList">
                        <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="notification-card <?php echo e($notification->is_read ? 'read' : 'unread'); ?>"
                                 data-id="<?php echo e($notification->id); ?>"
                                 data-read="<?php echo e($notification->is_read ? 'true' : 'false'); ?>"
                                 data-date="<?php echo e($notification->created_at->format('Y-m-d')); ?>">
                                <div class="card-body">
                                    <div class="d-flex align-items-start">
                                        <div class="me-2">
                                            <input type="checkbox" class="form-check-input notification-checkbox"
                                                   value="<?php echo e($notification->id); ?>">
                                        </div>

                                        <div class="notification-icon bg-<?php echo e($notification->color); ?> me-3">
                                            <i class="fa <?php echo e($notification->icon); ?>"></i>
                                        </div>

                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1 <?php echo e(!$notification->is_read ? 'fw-bold' : ''); ?>">
                                                        <?php echo e($notification->title); ?>

                                                        <?php if(!$notification->is_read): ?>
                                                            <span class="badge badge-primary badge-sm">جديد</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="mb-2 text-muted"><?php echo e($notification->message); ?></p>
                                                    <div class="notification-time">
                                                        <i class="fa fa-clock me-1"></i>
                                                        <?php echo e($notification->created_at->diffForHumans()); ?>

                                                    </div>
                                                </div>

                                                <div class="notification-actions">
                                                    <?php if(!$notification->is_read): ?>
                                                        <button class="btn btn-sm btn-outline-success mark-read-btn"
                                                                data-id="<?php echo e($notification->id); ?>" title="تحديد كمقروء">
                                                            <i class="fa fa-eye"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-outline-danger delete-btn"
                                                            data-id="<?php echo e($notification->id); ?>" title="حذف">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <?php if($notification->data): ?>
                                                <div class="mt-2">
                                                    <button class="btn btn-sm btn-outline-info toggle-details"
                                                            data-target="#details-<?php echo e($notification->id); ?>">
                                                        <i class="fa fa-info-circle me-1"></i>
                                                        تفاصيل إضافية
                                                    </button>
                                                    <div id="details-<?php echo e($notification->id); ?>" class="mt-2" style="display: none;">
                                                        <div class="alert alert-light">
                                                            <?php $__currentLoopData = $notification->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <small><strong><?php echo e($key); ?>:</strong> <?php echo e($value); ?></small><br>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- الترقيم -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($notifications->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fa fa-bell-slash"></i>
                        <h4>لا توجد إشعارات</h4>
                        <p>لم يتم العثور على أي إشعارات حتى الآن.</p>
                        <button class="btn btn-primary" id="createTestNotification">
                            <i class="fa fa-plus me-2"></i>
                            إنشاء إشعار تجريبي
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تصفية الإشعارات
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            const filter = this.dataset.filter;
            const notifications = document.querySelectorAll('.notification-card');

            notifications.forEach(notification => {
                let show = true;

                switch(filter) {
                    case 'unread':
                        show = notification.dataset.read === 'false';
                        break;
                    case 'read':
                        show = notification.dataset.read === 'true';
                        break;
                    case 'today':
                        const today = new Date().toISOString().split('T')[0];
                        show = notification.dataset.date === today;
                        break;
                    case 'all':
                    default:
                        show = true;
                        break;
                }

                notification.style.display = show ? 'block' : 'none';
            });
        });
    });

    // تحديد/إلغاء تحديد جميع الإشعارات
    document.getElementById('selectAllBtn').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.notification-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        this.innerHTML = allChecked
            ? '<i class="fa fa-check-square me-1"></i> تحديد الكل'
            : '<i class="fa fa-square me-1"></i> إلغاء التحديد';
    });

    // تحديد الإشعارات المحددة كمقروءة
    document.getElementById('markSelectedReadBtn').addEventListener('click', function() {
        const selectedIds = getSelectedNotificationIds();
        if (selectedIds.length === 0) {
            alert('يرجى تحديد إشعارات أولاً');
            return;
        }

        markMultipleAsRead(selectedIds);
    });

    // حذف الإشعارات المحددة
    document.getElementById('deleteSelectedBtn').addEventListener('click', function() {
        const selectedIds = getSelectedNotificationIds();
        if (selectedIds.length === 0) {
            alert('يرجى تحديد إشعارات أولاً');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف ${selectedIds.length} إشعار؟`)) {
            deleteMultipleNotifications(selectedIds);
        }
    });

    // تحديد إشعار واحد كمقروء
    document.querySelectorAll('.mark-read-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.dataset.id;
            markAsRead(id);
        });
    });

    // حذف إشعار واحد
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.dataset.id;
            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                deleteNotification(id);
            }
        });
    });

    // إظهار/إخفاء التفاصيل الإضافية
    document.querySelectorAll('.toggle-details').forEach(btn => {
        btn.addEventListener('click', function() {
            const target = document.querySelector(this.dataset.target);
            if (target.style.display === 'none') {
                target.style.display = 'block';
                this.innerHTML = '<i class="fa fa-eye-slash me-1"></i> إخفاء التفاصيل';
            } else {
                target.style.display = 'none';
                this.innerHTML = '<i class="fa fa-info-circle me-1"></i> تفاصيل إضافية';
            }
        });
    });

    // إنشاء إشعار تجريبي
    document.getElementById('testNotificationBtn')?.addEventListener('click', function() {
        if (window.notificationSystem) {
            window.notificationSystem.createTestNotification();
            setTimeout(() => location.reload(), 2000);
        }
    });

    document.getElementById('createTestNotification')?.addEventListener('click', function() {
        if (window.notificationSystem) {
            window.notificationSystem.createTestNotification();
            setTimeout(() => location.reload(), 2000);
        }
    });

    // الوظائف المساعدة
    function getSelectedNotificationIds() {
        return Array.from(document.querySelectorAll('.notification-checkbox:checked'))
                   .map(cb => cb.value);
    }

    async function markAsRead(id) {
        try {
            const response = await fetch(`/notifications/${id}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (response.ok) {
                location.reload();
            }
        } catch (error) {
            console.error('خطأ في تحديد الإشعار كمقروء:', error);
        }
    }

    async function markMultipleAsRead(ids) {
        try {
            const response = await fetch('/notifications/mark-selected-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ ids: ids })
            });

            const result = await response.json();
            if (result.success) {
                showAlert('success', result.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('error', result.message || 'حدث خطأ');
            }
        } catch (error) {
            console.error('خطأ في تحديد الإشعارات كمقروءة:', error);
            showAlert('error', 'حدث خطأ في تحديد الإشعارات');
        }
    }

    async function deleteNotification(id) {
        try {
            const response = await fetch(`/notifications/${id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (response.ok) {
                showAlert('success', 'تم حذف الإشعار بنجاح');
                setTimeout(() => location.reload(), 1000);
            }
        } catch (error) {
            console.error('خطأ في حذف الإشعار:', error);
            showAlert('error', 'حدث خطأ في حذف الإشعار');
        }
    }

    async function deleteMultipleNotifications(ids) {
        try {
            const response = await fetch('/notifications/delete-selected', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ ids: ids })
            });

            const result = await response.json();
            if (result.success) {
                showAlert('success', result.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('error', result.message || 'حدث خطأ');
            }
        } catch (error) {
            console.error('خطأ في حذف الإشعارات:', error);
            showAlert('error', 'حدث خطأ في حذف الإشعارات');
        }
    }

    // دالة عرض التنبيهات
    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nc\resources\views/notifications/index.blade.php ENDPATH**/ ?>