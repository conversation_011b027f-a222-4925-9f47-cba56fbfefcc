<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class clientssubscriptions extends Model
{
    use HasFactory;

    protected $table = "clientssubscriptions";
    protected $fillable =
        [
            'clientssubscriptionsname',
            'startdateclientssubscriptions',
            'timeenddateclientssubscriptions',
            'clients_id',
            'subscriptions_id',
            'clientssubscriptions_id',
            'startdateofcustomerclientssubscriptions',
            'endtdateofcustomerclientssubscriptions',
            'startdateofcustomersubscriptions',
            'endtdateofcustomersubscriptions',


        ];
    public $timestamps = true;


    public function clientschallengesssub()
    {
        return $this->belongsTo('App\Models\subscriptions','subscriptions_id');
    }


    public function historycl()
{
    return $this->hasMany( historycl::class,'clientssubscriptions_id');

}


}
