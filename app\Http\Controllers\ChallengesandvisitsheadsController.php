<?php

namespace App\Http\Controllers;

use App\Models\challengesandvisitsheads;
use Illuminate\Http\Request;

class ChallengesandvisitsheadsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(challengesandvisitsheads $challengesandvisitsheads)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(challengesandvisitsheads $challengesandvisitsheads)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, challengesandvisitsheads $challengesandvisitsheads)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(challengesandvisitsheads $challengesandvisitsheads)
    {
        //
    }
}
