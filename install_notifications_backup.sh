#!/bin/bash

# سكريبت تثبيت نظام الإشعارات والنسخ الاحتياطي
# تأكد من تشغيل هذا السكريبت من مجلد المشروع الرئيسي

echo "🚀 بدء تثبيت نظام الإشعارات والنسخ الاحتياطي..."

# التحقق من وجود Laravel
if [ ! -f "artisan" ]; then
    echo "❌ خطأ: لم يتم العثور على ملف artisan. تأكد من تشغيل السكريبت من مجلد المشروع."
    exit 1
fi

echo "📋 1. تشغيل الهجرات..."
php artisan migrate --force

if [ $? -ne 0 ]; then
    echo "❌ فشل في تشغيل الهجرات"
    exit 1
fi

echo "✅ تم تشغيل الهجرات بنجاح"

echo "⚙️ 2. إنشاء الإعدادات الافتراضية..."

# إنشاء إعدادات النسخ الاحتياطي
php artisan tinker --execute="App\Models\BackupSetting::getSettings();"

# إنشاء إعدادات الإشعارات للمستخدمين الموجودين
php artisan tinker --execute="
\$users = App\Models\User::all();
foreach(\$users as \$user) {
    App\Models\NotificationSetting::getForUser(\$user->id);
}
echo 'تم إنشاء إعدادات الإشعارات للمستخدمين';
"

echo "✅ تم إنشاء الإعدادات الافتراضية"

echo "📁 3. إنشاء مجلدات النسخ الاحتياطي..."
mkdir -p storage/backups
mkdir -p storage/app/backups
mkdir -p storage/app/temp
mkdir -p public/assets/sounds

# تعيين الصلاحيات
chmod 755 storage/backups
chmod 755 storage/app/backups
chmod 755 storage/app/temp
chmod 755 public/assets/sounds

echo "✅ تم إنشاء المجلدات وتعيين الصلاحيات"

echo "🔊 4. التحقق من ملفات الأصوات..."

# قائمة الأصوات المطلوبة
sounds=("notification.mp3" "bell.mp3" "chime.mp3" "ding.mp3" "pop.mp3" "swoosh.mp3")

for sound in "${sounds[@]}"; do
    if [ ! -f "public/assets/sounds/$sound" ]; then
        echo "⚠️  تحذير: ملف الصوت $sound غير موجود"
        echo "   يرجى تحميل ملفات الأصوات إلى public/assets/sounds/"
    else
        echo "✅ تم العثور على $sound"
    fi
done

echo "🗄️ 5. التحقق من MySQL و mysqldump..."

# التحقق من MySQL
if command -v mysql &> /dev/null; then
    echo "✅ MySQL متوفر"
    mysql --version
else
    echo "❌ MySQL غير متوفر. يرجى تثبيت MySQL أولاً"
fi

# التحقق من mysqldump
if command -v mysqldump &> /dev/null; then
    echo "✅ mysqldump متوفر"
    mysqldump --version
else
    echo "❌ mysqldump غير متوفر. يرجى تثبيت MySQL client tools"
fi

echo "⏰ 6. إعداد جدولة المهام..."

# التحقق من crontab
if command -v crontab &> /dev/null; then
    echo "✅ crontab متوفر"
    
    # عرض التعليمات لإضافة cron job
    echo "📝 لإعداد جدولة المهام، أضف السطر التالي إلى crontab:"
    echo "   crontab -e"
    echo "   ثم أضف:"
    echo "   * * * * * cd $(pwd) && php artisan schedule:run >> /dev/null 2>&1"
    
else
    echo "❌ crontab غير متوفر"
fi

echo "🧪 7. اختبار النظام..."

# اختبار إنشاء إشعار تجريبي
echo "📢 إنشاء إشعار تجريبي..."
php artisan tinker --execute="
App\Models\Notification::createNotification([
    'type' => 'test',
    'title' => 'اختبار النظام',
    'message' => 'تم تثبيت نظام الإشعارات بنجاح!',
    'icon' => 'fa-check-circle',
    'color' => 'success',
    'user_id' => 1
]);
echo 'تم إنشاء إشعار تجريبي';
"

# اختبار النسخ الاحتياطي (اختياري)
read -p "هل تريد اختبار النسخ الاحتياطي؟ (y/n): " test_backup

if [ "$test_backup" = "y" ] || [ "$test_backup" = "Y" ]; then
    echo "💾 اختبار النسخ الاحتياطي..."
    php artisan backup:create --type=test
    
    if [ $? -eq 0 ]; then
        echo "✅ تم اختبار النسخ الاحتياطي بنجاح"
    else
        echo "❌ فشل في اختبار النسخ الاحتياطي"
    fi
fi

echo ""
echo "🎉 تم الانتهاء من التثبيت!"
echo ""
echo "📋 الخطوات التالية:"
echo "1. تحميل ملفات الأصوات إلى public/assets/sounds/"
echo "2. إعداد cron job للجدولة التلقائية"
echo "3. اختبار النظام من خلال الواجهة"
echo "4. مراجعة الإعدادات في صفحة الإشعارات والنسخ الاحتياطي"
echo ""
echo "🔗 الروابط المهمة:"
echo "- الإشعارات: /notifications"
echo "- إعدادات الإشعارات: /notifications/settings"
echo "- النسخ الاحتياطي: /backup"
echo ""
echo "📚 للمزيد من المعلومات، راجع:"
echo "- INSTALLATION_GUIDE.md"
echo "- NOTIFICATIONS_BACKUP_README.md"
echo ""
echo "✨ استمتع بنظام الإشعارات والنسخ الاحتياطي المتقدم!"
