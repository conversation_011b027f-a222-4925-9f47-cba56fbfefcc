<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('estps', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('estps_id');
            $table->string('question');
            $table->string('answer');
            $table->text('note')->nullable();
            $table->string('created_at_user');
            $table->foreign('estps_id')->references('id')->on('hed_estps')->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('estps');
    }
};
