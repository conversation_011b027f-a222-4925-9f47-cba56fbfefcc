<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_logs', function (Blueprint $table) {
            $table->id();
            $table->string('backup_type'); // daily, weekly, monthly, manual
            $table->string('file_name'); // اسم ملف النسخة الاحتياطية
            $table->string('file_path'); // مسار الملف
            $table->bigInteger('file_size')->default(0); // حجم الملف بالبايت
            $table->enum('status', ['success', 'failed', 'in_progress'])->default('in_progress');
            $table->text('error_message')->nullable(); // رسالة الخطأ في حالة الفشل
            $table->integer('duration')->nullable(); // مدة النسخ بالثواني
            $table->json('tables_backed_up')->nullable(); // الجداول التي تم نسخها
            $table->unsignedBigInteger('created_by')->nullable(); // المستخدم الذي أنشأ النسخة
            $table->timestamps();

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->index(['backup_type', 'status']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_logs');
    }
};
