<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
class hed_estps extends Model
{




    use HasFactory;
    protected $guarded = [];

    //

##########

public function Estps()
{

    // return $this->belongsTo('App\hed_estps');
    return $this->hasOne('App\Models\estps','estps_id');

}

#########################


#########

public function cliennnnnt()
{
    return $this->belongsTo('App\Models\client','id');
}

}
