<?php

/**
 * إنشاء ملفات صوتية بسيطة للإشعارات
 */

echo "🔊 إنشاء ملفات صوتية للإشعارات...\n";

// إنشاء مجلد الأصوات إذا لم يكن موجوداً
$soundsDir = 'public/assets/sounds/';
if (!is_dir($soundsDir)) {
    mkdir($soundsDir, 0755, true);
    echo "📁 تم إنشاء مجلد الأصوات\n";
}

// إنشاء ملف صوتي بسيط باستخدام data URL
$sounds = [
    'notification.mp3' => 'صوت إشعار افتراضي',
    'bell.mp3' => 'صوت جرس',
    'chime.mp3' => 'نغمة موسيقية',
    'ding.mp3' => 'صوت دينغ',
    'pop.mp3' => 'صوت بوب',
    'swoosh.mp3' => 'صوت سووش'
];

// إنشاء ملف HTML لتوليد الأصوات
$htmlContent = '<!DOCTYPE html>
<html>
<head>
    <title>مولد الأصوات</title>
</head>
<body>
    <h1>مولد الأصوات للإشعارات</h1>
    <p>استخدم هذه الصفحة لتوليد أصوات بسيطة:</p>
    
    <script>
    // إنشاء صوت بسيط باستخدام Web Audio API
    function createBeep(frequency = 800, duration = 200, volume = 0.3) {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = frequency;
        oscillator.type = "sine";
        
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration / 1000);
    }
    
    // أزرار لتجربة الأصوات
    document.addEventListener("DOMContentLoaded", function() {
        const sounds = {
            "notification": () => createBeep(800, 300, 0.3),
            "bell": () => createBeep(1000, 500, 0.4),
            "chime": () => { createBeep(800, 200); setTimeout(() => createBeep(1000, 200), 100); },
            "ding": () => createBeep(1200, 150, 0.5),
            "pop": () => createBeep(600, 100, 0.3),
            "swoosh": () => {
                for(let i = 0; i < 10; i++) {
                    setTimeout(() => createBeep(400 + i * 50, 50, 0.2), i * 20);
                }
            }
        };
        
        Object.keys(sounds).forEach(soundName => {
            const button = document.createElement("button");
            button.textContent = `تشغيل ${soundName}`;
            button.onclick = sounds[soundName];
            button.style.margin = "5px";
            button.style.padding = "10px";
            document.body.appendChild(button);
            document.body.appendChild(document.createElement("br"));
        });
    });
    </script>
</body>
</html>';

file_put_contents($soundsDir . 'sound_generator.html', $htmlContent);
echo "✅ تم إنشاء مولد الأصوات: {$soundsDir}sound_generator.html\n";

// إنشاء ملف JavaScript لتوليد الأصوات
$jsContent = '// مولد الأصوات البسيط
class SimpleAudioGenerator {
    static createBeep(frequency = 800, duration = 200, volume = 0.3) {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = frequency;
            oscillator.type = "sine";
            
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration / 1000);
            
            return true;
        } catch (error) {
            console.error("خطأ في توليد الصوت:", error);
            return false;
        }
    }
    
    static playNotificationSound() {
        return this.createBeep(800, 300, 0.3);
    }
    
    static playBellSound() {
        return this.createBeep(1000, 500, 0.4);
    }
    
    static playChimeSound() {
        this.createBeep(800, 200);
        setTimeout(() => this.createBeep(1000, 200), 100);
        return true;
    }
    
    static playDingSound() {
        return this.createBeep(1200, 150, 0.5);
    }
    
    static playPopSound() {
        return this.createBeep(600, 100, 0.3);
    }
    
    static playSwooshSound() {
        for(let i = 0; i < 10; i++) {
            setTimeout(() => this.createBeep(400 + i * 50, 50, 0.2), i * 20);
        }
        return true;
    }
}

// تصدير للاستخدام العام
window.SimpleAudioGenerator = SimpleAudioGenerator;';

file_put_contents($soundsDir . 'simple_audio.js', $jsContent);
echo "✅ تم إنشاء مكتبة الأصوات البسيطة: {$soundsDir}simple_audio.js\n";

// إنشاء ملفات صوتية وهمية صغيرة
foreach ($sounds as $filename => $description) {
    $filePath = $soundsDir . $filename;
    
    // إنشاء ملف صوتي وهمي صغير (header MP3 بسيط)
    $mp3Header = "\xFF\xFB\x90\x00"; // MP3 header بسيط
    $mp3Data = str_repeat("\x00", 1000); // بيانات صامتة
    
    file_put_contents($filePath, $mp3Header . $mp3Data);
    echo "📄 تم إنشاء ملف صوتي وهمي: {$filename} ({$description})\n";
}

echo "\n💡 ملاحظات مهمة:\n";
echo "1. الملفات المُنشأة هي ملفات وهمية للاختبار فقط\n";
echo "2. للحصول على أصوات حقيقية، حمل ملفات MP3 من:\n";
echo "   - Freesound.org\n";
echo "   - Pixabay.com\n";
echo "   - Zapsplat.com\n";
echo "3. استبدل الملفات الوهمية بملفات MP3 حقيقية\n";
echo "4. افتح {$soundsDir}sound_generator.html في المتصفح لتجربة الأصوات المولدة\n";

echo "\n🎉 تم الانتهاء من إنشاء ملفات الأصوات!\n";

?>
