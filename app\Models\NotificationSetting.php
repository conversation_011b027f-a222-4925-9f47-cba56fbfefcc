<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NotificationSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'sound_enabled',
        'sound_file',
        'sound_volume',
        'desktop_notifications',
        'new_client_notifications',
        'new_visit_notifications',
        'payment_notifications',
        'challenge_notifications'
    ];

    protected $casts = [
        'sound_enabled' => 'boolean',
        'desktop_notifications' => 'boolean',
        'new_client_notifications' => 'boolean',
        'new_visit_notifications' => 'boolean',
        'payment_notifications' => 'boolean',
        'challenge_notifications' => 'boolean'
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * الحصول على إعدادات المستخدم أو إنشاء إعدادات افتراضية
     */
    public static function getForUser($userId)
    {
        return static::firstOrCreate(
            ['user_id' => $userId],
            [
                'sound_enabled' => true,
                'sound_file' => 'notification.mp3',
                'sound_volume' => 50,
                'desktop_notifications' => true,
                'new_client_notifications' => true,
                'new_visit_notifications' => true,
                'payment_notifications' => true,
                'challenge_notifications' => true
            ]
        );
    }

    /**
     * تحديث إعدادات المستخدم
     */
    public static function updateForUser($userId, $settings)
    {
        $notificationSetting = static::getForUser($userId);
        $notificationSetting->update($settings);
        return $notificationSetting;
    }

    /**
     * التحقق من تفعيل نوع إشعار معين للمستخدم
     */
    public static function isEnabledForUser($userId, $notificationType)
    {
        $settings = static::getForUser($userId);
        
        switch ($notificationType) {
            case 'new_client':
                return $settings->new_client_notifications;
            case 'new_visit':
                return $settings->new_visit_notifications;
            case 'payment':
                return $settings->payment_notifications;
            case 'challenge':
                return $settings->challenge_notifications;
            default:
                return true;
        }
    }

    /**
     * الحصول على ملفات الأصوات المتاحة
     */
    public static function getAvailableSounds()
    {
        return [
            'notification.mp3' => 'صوت افتراضي',
            'bell.mp3' => 'جرس',
            'chime.mp3' => 'نغمة',
            'ding.mp3' => 'دينغ',
            'pop.mp3' => 'بوب',
            'swoosh.mp3' => 'سووش'
        ];
    }
}
