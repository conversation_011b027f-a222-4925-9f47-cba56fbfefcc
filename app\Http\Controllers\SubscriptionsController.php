<?php

namespace App\Http\Controllers;

use App\Models\subscriptions;
use Illuminate\Http\Request;

class SubscriptionsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $subscriptions = subscriptions::all();
        return view('subscriptions.subscriptions',compact('subscriptions'));
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {


        $validatedData = $request->validate([
            'subscriptionsname' => 'required|unique:subscriptions|max:255',
        ],[

            'subscriptionsname.required' =>'يرجي ادخال اسم الاشتراك',
            'subscriptionsname.unique' =>'اسم الاشتراك مسجل مسبقا',


        ]);

        subscriptions::create([
        'subscriptionsname' => $request->subscriptionsname,
        'startdatesubscriptions' => $request->startdatesubscriptions,
        'timeenddatesubscriptions' => $request->  timeenddatesubscriptions,



        ]);
        session()->flash('Add', 'تم اضافة الاشتراك بنجاح ');
        return redirect()->back();


    }



    /**
     * Display the specified resource.
     */
    public function show(subscriptions $subscriptions)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(subscriptions $subscriptions)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)

        {

            $id = $request->id;


            $validate = $request->validate([


                'subscriptionsname' => 'required|max:255|unique:subscriptions,subscriptionsname,'.$id,
            ],[

                'subscriptionsname.required' =>'يرجي ادخال اسم الاشتراك',
                'subscriptionsname.unique' =>'اسم الاشتراك مسجل مسبقا',

            ]);

            $subscriptions = subscriptions::find($id);
            $subscriptions->update([
                'subscriptionsname' => $request->subscriptionsname,
                'startdatesubscriptions' => $request->startdatesubscriptions,
                'timeenddatesubscriptions' => $request->timeenddatesubscriptions,



            ]);

            session()->flash('edit','تم تعديل الاشتراك بنجاج');
            return redirect()->back();
        }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {

        $id = $request->id;
        subscriptions::find($id)->delete();
        session()->flash('delete','تم حذف الاشتراك بنجاح');
        return redirect()->back();
        //
    }
}
