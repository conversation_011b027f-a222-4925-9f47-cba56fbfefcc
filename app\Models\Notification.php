<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'title',
        'message',
        'icon',
        'color',
        'user_id',
        'related_id',
        'related_type',
        'is_read',
        'read_at',
        'data'
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime'
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * الحصول على العنصر المرتبط
     */
    public function related()
    {
        return $this->morphTo();
    }

    /**
     * تحديد الإشعار كمقروء
     */
    public function markAsRead()
    {
        $this->update([
            'is_read' => true,
            'read_at' => now()
        ]);
    }

    /**
     * الحصول على الإشعارات غير المقروءة للمستخدم
     */
    public static function unreadForUser($userId)
    {
        return static::where('user_id', $userId)
            ->where('is_read', false)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * الحصول على عدد الإشعارات غير المقروءة للمستخدم
     */
    public static function unreadCountForUser($userId)
    {
        return static::where('user_id', $userId)
            ->where('is_read', false)
            ->count();
    }

    /**
     * إنشاء إشعار جديد
     */
    public static function createNotification($data)
    {
        return static::create([
            'type' => $data['type'],
            'title' => $data['title'],
            'message' => $data['message'],
            'icon' => $data['icon'] ?? 'fa-bell',
            'color' => $data['color'] ?? 'primary',
            'user_id' => $data['user_id'] ?? null,
            'related_id' => $data['related_id'] ?? null,
            'related_type' => $data['related_type'] ?? null,
            'data' => $data['data'] ?? null
        ]);
    }

    /**
     * إنشاء إشعار لجميع المستخدمين
     */
    public static function createForAllUsers($data)
    {
        $users = User::all();
        $notifications = [];

        foreach ($users as $user) {
            $notificationData = array_merge($data, ['user_id' => $user->id]);
            $notifications[] = static::createNotification($notificationData);
        }

        return $notifications;
    }

    /**
     * إنشاء إشعار للمستخدمين حسب الدور
     */
    public static function createForRole($role, $data)
    {
        $users = User::where('role', $role)->get();
        $notifications = [];

        foreach ($users as $user) {
            $notificationData = array_merge($data, ['user_id' => $user->id]);
            $notifications[] = static::createNotification($notificationData);
        }

        return $notifications;
    }

    /**
     * حذف الإشعارات القديمة
     */
    public static function deleteOldNotifications($days = 30)
    {
        return static::where('created_at', '<', now()->subDays($days))->delete();
    }
}
