<?php

/**
 * إصلاح جميع المشاكل - الإصدار النهائي
 * تشغيل: php fix_all_issues.php
 */

echo "🚀 إصلاح جميع مشاكل نظام الإشعارات - الإصدار النهائي\n";
echo "=" . str_repeat("=", 65) . "\n\n";

// تحميل Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل Laravel: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. مسح جميع أنواع الـ cache
echo "1️⃣ مسح cache شامل...\n";
$cacheCommands = [
    'php artisan cache:clear' => 'Application Cache',
    'php artisan config:clear' => 'Configuration Cache', 
    'php artisan route:clear' => 'Route Cache',
    'php artisan view:clear' => 'View Cache',
    'php artisan optimize:clear' => 'All Optimization'
];

foreach ($cacheCommands as $command => $description) {
    exec($command, $output, $return);
    echo ($return === 0 ? "✅" : "⚠️ ") . " {$description}\n";
}

// تحديث autoloader
exec('composer dump-autoload', $output, $return);
echo ($return === 0 ? "✅" : "❌") . " Composer Autoloader\n\n";

// 2. إنشاء المجلدات المطلوبة
echo "2️⃣ إنشاء المجلدات المطلوبة...\n";
$directories = [
    'storage/backups' => 0755,
    'storage/app/backups' => 0755,
    'storage/app/temp' => 0755,
    'public/assets/sounds' => 0755,
    'public/assets/css' => 0755,
    'public/assets/js' => 0755
];

foreach ($directories as $dir => $permission) {
    if (!is_dir($dir)) {
        mkdir($dir, $permission, true);
        echo "📁 تم إنشاء: {$dir}\n";
    } else {
        chmod($dir, $permission);
        echo "✅ موجود: {$dir}\n";
    }
}
echo "\n";

// 3. تشغيل الهجرات
echo "3️⃣ تشغيل الهجرات...\n";
exec('php artisan migrate --force', $output, $return);
echo ($return === 0 ? "✅" : "⚠️ ") . " Database Migrations\n\n";

// 4. إنشاء الإعدادات الافتراضية
echo "4️⃣ إنشاء الإعدادات الافتراضية...\n";
try {
    // إعدادات النسخ الاحتياطي
    $backupSettings = App\Models\BackupSetting::getSettings();
    echo "✅ إعدادات النسخ الاحتياطي\n";
    
    // إعدادات الإشعارات للمستخدمين
    $users = App\Models\User::all();
    foreach ($users as $user) {
        $setting = App\Models\NotificationSetting::firstOrCreate(
            ['user_id' => $user->id],
            [
                'sound_enabled' => true,
                'sound_file' => 'notification.mp3',
                'sound_volume' => 50,
                'desktop_notifications' => true,
                'new_client_notifications' => true,
                'backup_notifications' => true,
                'system_notifications' => true
            ]
        );
    }
    echo "✅ إعدادات الإشعارات للمستخدمين\n";
    
} catch (Exception $e) {
    echo "⚠️  تحذير في الإعدادات: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. إنشاء ملفات الأصوات التجريبية
echo "5️⃣ إنشاء ملفات الأصوات...\n";
$sounds = [
    'notification.mp3' => 'الصوت الافتراضي',
    'bell.mp3' => 'صوت جرس',
    'chime.mp3' => 'نغمة موسيقية',
    'ding.mp3' => 'صوت دينغ',
    'pop.mp3' => 'صوت بوب',
    'swoosh.mp3' => 'صوت سووش'
];

foreach ($sounds as $sound => $description) {
    $soundPath = "public/assets/sounds/{$sound}";
    if (!file_exists($soundPath)) {
        file_put_contents($soundPath, "Placeholder for {$sound} - {$description}");
        echo "📄 تم إنشاء مرجع: {$sound}\n";
    } else {
        echo "✅ موجود: {$sound}\n";
    }
}
echo "\n";

// 6. اختبار Observer للعملاء
echo "6️⃣ اختبار Observer للعملاء...\n";
try {
    // تسجيل دخول مستخدم تجريبي
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        echo "✅ تم تسجيل دخول المستخدم: {$testUser->name}\n";
        
        // إنشاء عميل تجريبي
        $testClient = App\Models\clients::create([
            'name' => 'عميل تجريبي للاختبار - ' . date('H:i:s'),
            'gender' => 'male',
            'phone' => '59' . rand(1000000, 9999999),
            'subscroptiondate' => now()->toDateString(),
            'note' => 'عميل تجريبي لاختبار Observer',
            'password' => '123456',
            'roles_name' => 'yes',
            'active' => 'yes',
            'status' => 'active'
        ]);
        
        echo "✅ تم إنشاء عميل تجريبي: {$testClient->name}\n";
        
        // انتظار للسماح للـ Observer بالعمل
        sleep(1);
        
        // فحص الإشعارات
        $notifications = App\Models\Notification::where('type', 'new_client')
                                              ->where('related_id', $testClient->id)
                                              ->get();
        
        echo "✅ عدد الإشعارات المُنشأة: " . $notifications->count() . "\n";
        
        if ($notifications->count() > 0) {
            echo "🎉 Observer يعمل بشكل صحيح!\n";
            foreach ($notifications as $notification) {
                $user = App\Models\User::find($notification->user_id);
                echo "   - إشعار للمستخدم: {$user->name}\n";
            }
        } else {
            echo "❌ Observer لا يعمل! تحقق من التسجيل في AppServiceProvider\n";
        }
        
        // تنظيف البيانات التجريبية
        App\Models\Notification::where('related_id', $testClient->id)->delete();
        $testClient->delete();
        echo "✅ تم تنظيف البيانات التجريبية\n";
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار Observer: " . $e->getMessage() . "\n";
}
echo "\n";

// 7. إنشاء إشعار ترحيبي
echo "7️⃣ إنشاء إشعار ترحيبي...\n";
try {
    $users = App\Models\User::all();
    foreach ($users as $user) {
        App\Models\Notification::createNotification([
            'type' => 'system_ready',
            'title' => '🎉 النظام جاهز!',
            'message' => 'تم إعداد نظام الإشعارات والنسخ الاحتياطي بنجاح. جميع الميزات تعمل الآن!',
            'icon' => 'fa-check-circle',
            'color' => 'success',
            'user_id' => $user->id
        ]);
    }
    echo "✅ تم إنشاء إشعارات ترحيبية لجميع المستخدمين\n";
} catch (Exception $e) {
    echo "⚠️  تحذير في الإشعار الترحيبي: " . $e->getMessage() . "\n";
}
echo "\n";

// 8. فحص شامل للنظام
echo "8️⃣ فحص شامل للنظام...\n";

// فحص الملفات المطلوبة
$requiredFiles = [
    'app/Http/Controllers/NotificationController.php' => 'Notification Controller',
    'app/Http/Controllers/BackupController.php' => 'Backup Controller',
    'app/Models/Notification.php' => 'Notification Model',
    'app/Models/NotificationSetting.php' => 'NotificationSetting Model',
    'app/Models/BackupSetting.php' => 'BackupSetting Model',
    'app/Models/BackupLog.php' => 'BackupLog Model',
    'app/Observers/ClientObserver.php' => 'Client Observer',
    'public/assets/js/notifications.js' => 'Notifications JavaScript',
    'public/assets/css/notifications.css' => 'Notifications CSS',
    'resources/views/notifications/index.blade.php' => 'Notifications Index View',
    'resources/views/notifications/settings.blade.php' => 'Notifications Settings View',
    'resources/views/backup/index.blade.php' => 'Backup Index View'
];

foreach ($requiredFiles as $file => $description) {
    echo (file_exists($file) ? "✅" : "❌") . " {$description}\n";
}

// فحص الجداول
echo "\n📊 فحص قاعدة البيانات:\n";
$tables = ['notifications', 'notification_settings', 'backup_settings', 'backup_logs'];
foreach ($tables as $table) {
    try {
        $count = DB::table($table)->count();
        echo "✅ جدول {$table}: {$count} سجل\n";
    } catch (Exception $e) {
        echo "❌ جدول {$table}: غير موجود\n";
    }
}

// فحص المسارات
echo "\n🛣️  فحص المسارات:\n";
$routes = [
    'notifications.index' => '/notifications',
    'notifications.settings' => '/notifications/settings',
    'backup.index' => '/backup'
];

foreach ($routes as $routeName => $expectedPath) {
    try {
        $url = route($routeName);
        echo "✅ مسار {$routeName}: {$url}\n";
    } catch (Exception $e) {
        echo "❌ مسار {$routeName}: غير موجود\n";
    }
}
echo "\n";

// 9. التقرير النهائي
echo "📋 التقرير النهائي:\n";
echo "=" . str_repeat("=", 20) . "\n";

$totalNotifications = DB::table('notifications')->count();
$unreadNotifications = DB::table('notifications')->where('is_read', false)->count();
$totalUsers = DB::table('users')->count();
$totalClients = DB::table('clients')->count();

echo "📈 الإحصائيات:\n";
echo "   - إجمالي الإشعارات: {$totalNotifications}\n";
echo "   - الإشعارات غير المقروءة: {$unreadNotifications}\n";
echo "   - إجمالي المستخدمين: {$totalUsers}\n";
echo "   - إجمالي العملاء: {$totalClients}\n\n";

echo "🎯 خطوات الاختبار:\n";
echo "1. زيارة /notifications - لعرض الإشعارات\n";
echo "2. زيارة /notifications/settings - للإعدادات\n";
echo "3. النقر على زر 'تفعيل إشعارات سطح المكتب بقوة'\n";
echo "4. اختبار الصوت من الإعدادات\n";
echo "5. زيارة /clients وإضافة عميل جديد\n";
echo "6. التحقق من ظهور إشعار العميل الجديد\n";
echo "7. زيارة /backup - للنسخ الاحتياطي\n\n";

echo "🔧 في حالة المشاكل:\n";
echo "   - تحقق من storage/logs/laravel.log\n";
echo "   - تحقق من console المتصفح\n";
echo "   - تشغيل: php test_client_observer.php\n";
echo "   - تشغيل: php test_complete_system.php\n\n";

echo "🎉 تم الانتهاء من الإصلاح الشامل!\n";
echo "💡 النظام جاهز للاستخدام. استمتع بالإشعارات والنسخ الاحتياطي!\n";

?>
