<?php

return [
    /*
    |--------------------------------------------------------------------------
    | إعدادات نظام الإشعارات
    |--------------------------------------------------------------------------
    |
    | هذا الملف يحتوي على جميع إعدادات نظام الإشعارات المتقدم
    |
    */

    'enabled' => env('NOTIFICATIONS_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | إعدادات الإشعارات الافتراضية
    |--------------------------------------------------------------------------
    */
    'defaults' => [
        'sound_enabled' => true,
        'sound_file' => 'notification.mp3',
        'sound_volume' => 50,
        'desktop_notifications' => true,
        'new_client_notifications' => true,
        'new_visit_notifications' => true,
        'payment_notifications' => true,
        'challenge_notifications' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | أنواع الإشعارات المتاحة
    |--------------------------------------------------------------------------
    */
    'types' => [
        'new_client' => [
            'name' => 'عميل جديد',
            'icon' => 'fa-user-plus',
            'color' => 'success',
            'roles' => ['super', 'admin', 'secretary']
        ],
        'client_updated' => [
            'name' => 'تحديث عميل',
            'icon' => 'fa-user-edit',
            'color' => 'info',
            'roles' => ['super', 'admin']
        ],
        'client_deleted' => [
            'name' => 'حذف عميل',
            'icon' => 'fa-user-times',
            'color' => 'danger',
            'roles' => ['super', 'admin']
        ],
        'new_visit' => [
            'name' => 'زيارة جديدة',
            'icon' => 'fa-calendar-plus',
            'color' => 'primary',
            'roles' => ['super', 'admin', 'user']
        ],
        'new_payment' => [
            'name' => 'دفعة جديدة',
            'icon' => 'fa-money-bill',
            'color' => 'success',
            'roles' => ['super', 'admin']
        ],
        'backup_success' => [
            'name' => 'نجح النسخ الاحتياطي',
            'icon' => 'fa-database',
            'color' => 'success',
            'roles' => ['super', 'admin']
        ],
        'backup_failed' => [
            'name' => 'فشل النسخ الاحتياطي',
            'icon' => 'fa-exclamation-triangle',
            'color' => 'danger',
            'roles' => ['super', 'admin']
        ],
        'system_alert' => [
            'name' => 'تنبيه النظام',
            'icon' => 'fa-exclamation-circle',
            'color' => 'warning',
            'roles' => ['super', 'admin']
        ],
        'test' => [
            'name' => 'إشعار تجريبي',
            'icon' => 'fa-flask',
            'color' => 'warning',
            'roles' => ['super', 'admin', 'user', 'secretary']
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | الأصوات المتاحة
    |--------------------------------------------------------------------------
    */
    'sounds' => [
        'notification.mp3' => 'صوت افتراضي',
        'bell.mp3' => 'جرس',
        'chime.mp3' => 'نغمة',
        'ding.mp3' => 'دينغ',
        'pop.mp3' => 'بوب',
        'swoosh.mp3' => 'سووش'
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التحقق من الإشعارات
    |--------------------------------------------------------------------------
    */
    'polling' => [
        'enabled' => true,
        'interval' => 30000, // 30 ثانية
        'max_notifications' => 50
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات تنظيف الإشعارات
    |--------------------------------------------------------------------------
    */
    'cleanup' => [
        'enabled' => true,
        'keep_days' => 30,
        'batch_size' => 100
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات إشعارات سطح المكتب
    |--------------------------------------------------------------------------
    */
    'desktop' => [
        'enabled' => true,
        'icon' => '/assets/img/logo.png',
        'badge' => '/assets/img/logo.png',
        'timeout' => 5000, // 5 ثوان
        'require_interaction' => true
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الأمان
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encrypt_data' => false,
        'sanitize_content' => true,
        'max_message_length' => 500,
        'allowed_html_tags' => '<b><i><u><strong><em>'
    ]
];
