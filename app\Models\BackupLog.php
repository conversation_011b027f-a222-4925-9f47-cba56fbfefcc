<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BackupLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'backup_type',
        'file_name',
        'file_path',
        'file_size',
        'status',
        'error_message',
        'duration',
        'tables_backed_up',
        'created_by'
    ];

    protected $casts = [
        'tables_backed_up' => 'array'
    ];

    /**
     * العلاقة مع المستخدم الذي أنشأ النسخة
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * تحديد النسخة كناجحة
     */
    public function markAsSuccess($duration = null, $fileSize = null)
    {
        $this->update([
            'status' => 'success',
            'duration' => $duration,
            'file_size' => $fileSize
        ]);
    }

    /**
     * تحديد النسخة كفاشلة
     */
    public function markAsFailed($errorMessage)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage
        ]);
    }

    /**
     * الحصول على حجم الملف بصيغة قابلة للقراءة
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return 'غير محدد';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * الحصول على المدة بصيغة قابلة للقراءة
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->duration) {
            return 'غير محدد';
        }

        $seconds = $this->duration;
        $minutes = floor($seconds / 60);
        $seconds = $seconds % 60;

        if ($minutes > 0) {
            return $minutes . ' دقيقة و ' . $seconds . ' ثانية';
        }

        return $seconds . ' ثانية';
    }

    /**
     * الحصول على النسخ الاحتياطية الناجحة
     */
    public static function successful()
    {
        return static::where('status', 'success');
    }

    /**
     * الحصول على النسخ الاحتياطية الفاشلة
     */
    public static function failed()
    {
        return static::where('status', 'failed');
    }

    /**
     * حذف النسخ القديمة
     */
    public static function deleteOldBackups($days = 30)
    {
        $oldBackups = static::where('created_at', '<', now()->subDays($days))->get();
        
        foreach ($oldBackups as $backup) {
            // حذف الملف من النظام
            if (file_exists($backup->file_path)) {
                unlink($backup->file_path);
            }
            
            // حذف السجل من قاعدة البيانات
            $backup->delete();
        }
        
        return $oldBackups->count();
    }
}
