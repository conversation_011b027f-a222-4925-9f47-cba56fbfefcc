# دليل تثبيت نظام الإشعارات والنسخ الاحتياطي

## المتطلبات الأساسية

- PHP 8.2+
- Laravel 11.31
- MySQL 5.7+
- Composer
- Node.js (للأصوات)

## خطوات التثبيت

### 1. تشغيل الهجرات (Migrations)

```bash
php artisan migrate
```

### 2. إنشاء الإعدادات الافتراضية

```bash
php artisan tinker
```

ثم تشغيل الأوامر التالية:

```php
// إنشاء إعدادات النسخ الاحتياطي الافتراضية
App\Models\BackupSetting::getSettings();

// إنشاء إعدادات الإشعارات للمستخدمين الموجودين
$users = App\Models\User::all();
foreach($users as $user) {
    App\Models\NotificationSetting::getForUser($user->id);
}
```

### 3. إضافة ملفات الأصوات

قم بتحميل ملفات الأصوات التالية إلى مجلد `public/assets/sounds/`:

- `notification.mp3` - الصوت الافتراضي
- `bell.mp3` - صوت جرس
- `chime.mp3` - صوت نغمة
- `ding.mp3` - صوت دينغ
- `pop.mp3` - صوت بوب
- `swoosh.mp3` - صوت سووش

يمكن الحصول على أصوات مجانية من:
- [Freesound.org](https://freesound.org)
- [Zapsplat.com](https://zapsplat.com)
- [Adobe Stock Audio](https://stock.adobe.com/audio)

### 4. إعداد جدولة المهام (Cron Jobs)

أضف السطر التالي إلى crontab:

```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### 5. إعداد قاعدة البيانات للنسخ الاحتياطي

تأكد من أن MySQL مثبت ومتاح من سطر الأوامر:

```bash
mysql --version
mysqldump --version
```

### 6. إعداد الصلاحيات

تأكد من أن مجلد `storage/backups` قابل للكتابة:

```bash
mkdir -p storage/backups
chmod 755 storage/backups
```

### 7. اختبار النظام

#### اختبار الإشعارات:
1. سجل دخول إلى النظام
2. اذهب إلى إعدادات الإشعارات
3. انقر على "إشعار تجريبي"
4. تحقق من ظهور الإشعار

#### اختبار النسخ الاحتياطي:
1. اذهب إلى صفحة النسخ الاحتياطي
2. انقر على "بدء النسخ الاحتياطي"
3. تحقق من إنشاء الملف في مجلد `storage/backups`

## الميزات المتاحة

### نظام الإشعارات

#### الإشعارات التلقائية:
- إشعار عند إضافة عميل جديد
- إشعار عند تحديث بيانات العميل
- إشعار عند حذف العميل
- إشعارات النسخ الاحتياطي (نجاح/فشل)

#### الميزات:
- إشعارات فورية في المتصفح
- إشعارات سطح المكتب
- أصوات قابلة للتخصيص
- عداد الإشعارات غير المقروءة
- تصفية الإشعارات
- إعدادات شخصية لكل مستخدم

### نظام النسخ الاحتياطي

#### أنواع النسخ:
- نسخ يومي تلقائي
- نسخ أسبوعي تلقائي
- نسخ شهري تلقائي
- نسخ يدوي

#### الميزات:
- ضغط النسخ الاحتياطي
- اختيار الجداول المراد نسخها
- حذف النسخ القديمة تلقائياً
- سجل مفصل للنسخ
- تحميل النسخ الاحتياطية
- اختبار الاتصال الخارجي

## استكشاف الأخطاء

### مشاكل الإشعارات

#### الإشعارات لا تظهر:
1. تحقق من تشغيل JavaScript
2. تحقق من إعدادات المتصفح للإشعارات
3. تحقق من console للأخطاء

#### الأصوات لا تعمل:
1. تحقق من وجود ملفات الأصوات
2. تحقق من إعدادات المتصفح للصوت
3. تحقق من مستوى الصوت في الإعدادات

### مشاكل النسخ الاحتياطي

#### فشل النسخ الاحتياطي:
1. تحقق من صلاحيات مجلد storage/backups
2. تحقق من توفر mysqldump
3. تحقق من إعدادات قاعدة البيانات
4. تحقق من المساحة المتاحة على القرص

#### النسخ التلقائي لا يعمل:
1. تحقق من إعداد cron job
2. تحقق من سجل Laravel (storage/logs)
3. تحقق من إعدادات النسخ الاحتياطي

## الأوامر المفيدة

### إنشاء نسخة احتياطية يدوية:
```bash
php artisan backup:create --type=manual
```

### نسخ جداول محددة:
```bash
php artisan backup:create --tables=users,clients,visits
```

### حذف النسخ القديمة:
```bash
php artisan tinker
App\Models\BackupLog::deleteOldBackups(30);
```

### إنشاء إشعار تجريبي:
```bash
php artisan tinker
App\Models\Notification::createNotification([
    'type' => 'test',
    'title' => 'إشعار تجريبي',
    'message' => 'هذا إشعار تجريبي',
    'user_id' => 1
]);
```

## الأمان

### حماية النسخ الاحتياطي:
- تأكد من أن مجلد النسخ الاحتياطي غير متاح عبر الويب
- استخدم كلمات مرور قوية لقاعدة البيانات
- قم بتشفير النسخ الاحتياطية المهمة

### حماية الإشعارات:
- تحقق من صلاحيات المستخدمين
- لا تعرض معلومات حساسة في الإشعارات
- استخدم HTTPS لحماية البيانات

## الدعم

في حالة وجود مشاكل:
1. تحقق من سجل Laravel في `storage/logs`
2. تحقق من console المتصفح للأخطاء JavaScript
3. تحقق من إعدادات الخادم والصلاحيات

## التحديثات المستقبلية

### ميزات مخطط لها:
- إشعارات البريد الإلكتروني
- نسخ احتياطي إلى التخزين السحابي
- تقارير مفصلة للنسخ الاحتياطي
- إشعارات SMS
- واجهة برمجة تطبيقات للإشعارات
