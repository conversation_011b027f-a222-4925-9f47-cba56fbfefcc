<?php

/**
 * اختبار نظام العملاء الجدد الجديد
 * تشغيل: php test_new_clients_system.php
 */

echo "🧪 اختبار نظام العملاء الجدد الجديد\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// تحميل Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل Laravel: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. مسح cache
echo "1️⃣ مسح cache...\n";
exec('php artisan cache:clear', $output, $return);
exec('php artisan config:clear', $output, $return);
exec('php artisan route:clear', $output, $return);
exec('composer dump-autoload', $output, $return);
echo "✅ تم مسح cache\n\n";

// 2. فحص الملفات الجديدة
echo "2️⃣ فحص الملفات الجديدة...\n";
$files = [
    'resources/views/components/new-clients-dropdown.blade.php' => 'قائمة العملاء الجدد',
    'public/assets/js/instant-notifications.js' => 'نظام الإشعارات الفورية'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$description}: موجود\n";
        $size = round(filesize($file) / 1024, 2);
        echo "   - الحجم: {$size} KB\n";
    } else {
        echo "❌ {$description}: مفقود\n";
    }
}
echo "\n";

// 3. اختبار APIs الجديدة
echo "3️⃣ اختبار APIs الجديدة...\n";
try {
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        
        // اختبار API العملاء الجدد
        $controller = new App\Http\Controllers\NotificationController();
        
        // 1. اختبار getNewClients
        $response = $controller->getNewClients();
        $result = json_decode($response->getContent(), true);
        
        if ($result['success']) {
            echo "✅ API getNewClients يعمل - عدد العملاء: {$result['count']}\n";
        } else {
            echo "❌ API getNewClients فشل\n";
        }
        
        // 2. اختبار getLastClientId
        $response = $controller->getLastClientId();
        $result = json_decode($response->getContent(), true);
        
        if ($result['success']) {
            echo "✅ API getLastClientId يعمل - آخر ID: {$result['last_id']}\n";
        } else {
            echo "❌ API getLastClientId فشل\n";
        }
        
        // 3. اختبار getNewClientsSince
        $response = $controller->getNewClientsSince(0);
        $result = json_decode($response->getContent(), true);
        
        if ($result['success']) {
            echo "✅ API getNewClientsSince يعمل - عدد العملاء: {$result['count']}\n";
        } else {
            echo "❌ API getNewClientsSince فشل\n";
        }
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار APIs: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار المسارات
echo "4️⃣ اختبار المسارات...\n";
try {
    $routes = [
        'api.new-clients' => '/api/new-clients',
        'api.last-client-id' => '/api/last-client-id',
        'api.new-clients-since' => '/api/new-clients-since/0'
    ];
    
    foreach ($routes as $routeName => $expectedPath) {
        try {
            if ($routeName === 'api.new-clients-since') {
                $url = route($routeName, ['lastId' => 0]);
            } else {
                $url = route($routeName);
            }
            echo "✅ مسار {$routeName}: {$url}\n";
        } catch (Exception $e) {
            echo "❌ مسار {$routeName}: غير موجود\n";
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص المسارات: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. إنشاء عملاء تجريبيين
echo "5️⃣ إنشاء عملاء تجريبيين...\n";
try {
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        
        $testClients = [];
        for ($i = 1; $i <= 3; $i++) {
            $client = App\Models\clients::create([
                'name' => "عميل تجريبي {$i} - " . date('H:i:s'),
                'gender' => $i % 2 == 0 ? 'female' : 'male',
                'phone' => '59' . rand(1000000, 9999999),
                'subscroptiondate' => now()->toDateString(),
                'note' => "عميل تجريبي رقم {$i} لاختبار النظام الجديد",
                'password' => '123456',
                'roles_name' => 'yes',
                'active' => 'yes',
                'status' => 'active'
            ]);
            
            $testClients[] = $client;
            echo "✅ تم إنشاء عميل تجريبي {$i}: {$client->name} (ID: {$client->id})\n";
        }
        
        // انتظار للسماح للـ Observer بالعمل
        sleep(2);
        
        // فحص الإشعارات المُنشأة
        $notifications = App\Models\Notification::where('type', 'new_client')
                                              ->whereIn('related_id', array_column($testClients, 'id'))
                                              ->get();
        
        echo "✅ عدد الإشعارات المُنشأة: " . $notifications->count() . "\n";
        
        // اختبار API مع العملاء الجدد
        $controller = new App\Http\Controllers\NotificationController();
        $response = $controller->getNewClients();
        $result = json_decode($response->getContent(), true);
        
        echo "✅ API يعرض {$result['count']} عميل جديد\n";
        
        if ($result['count'] > 0) {
            echo "📋 العملاء الجدد:\n";
            foreach ($result['clients'] as $client) {
                echo "   - {$client['name']} ({$client['phone']}) - {$client['created_at_human']}\n";
            }
        }
        
        // تنظيف البيانات التجريبية
        foreach ($testClients as $client) {
            App\Models\Notification::where('related_id', $client->id)->delete();
            $client->delete();
        }
        echo "✅ تم تنظيف البيانات التجريبية\n";
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء العملاء التجريبيين: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. فحص تضمين الملفات في الـ layout
echo "6️⃣ فحص تضمين الملفات في الـ layout...\n";
try {
    $headerFile = 'resources/views/layouts/main-header.blade.php';
    if (file_exists($headerFile)) {
        $content = file_get_contents($headerFile);
        
        if (strpos($content, 'new-clients-dropdown') !== false) {
            echo "✅ قائمة العملاء الجدد مضمنة في الهيدر\n";
        } else {
            echo "❌ قائمة العملاء الجدد غير مضمنة في الهيدر\n";
        }
        
        if (strpos($content, 'instant-notifications.js') !== false) {
            echo "✅ نظام الإشعارات الفورية مضمن في الهيدر\n";
        } else {
            echo "❌ نظام الإشعارات الفورية غير مضمن في الهيدر\n";
        }
        
        // فحص إزالة الجرس القديم
        if (strpos($content, 'notification-bell') === false) {
            echo "✅ تم إزالة الجرس القديم\n";
        } else {
            echo "⚠️  الجرس القديم لا يزال موجوداً\n";
        }
        
    } else {
        echo "❌ ملف الهيدر غير موجود\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص الهيدر: " . $e->getMessage() . "\n";
}
echo "\n";

// 7. التقرير النهائي
echo "📊 التقرير النهائي:\n";
echo "=" . str_repeat("=", 20) . "\n";

$totalClients = DB::table('clients')->count();
$newClientsToday = DB::table('clients')->whereDate('created_at', today())->count();
$totalNotifications = DB::table('notifications')->count();

echo "📈 الإحصائيات:\n";
echo "   - إجمالي العملاء: {$totalClients}\n";
echo "   - العملاء الجدد اليوم: {$newClientsToday}\n";
echo "   - إجمالي الإشعارات: {$totalNotifications}\n\n";

echo "🎯 النظام الجديد:\n";
echo "   ✅ قائمة العملاء الجدد الجميلة بدلاً من الجرس\n";
echo "   ✅ نظام إشعارات فوري لجميع الأجهزة\n";
echo "   ✅ تحديث كل 5 ثوان للسرعة الفائقة\n";
echo "   ✅ إشعارات سطح المكتب مع أصوات مميزة\n";
echo "   ✅ إشعارات داخل الصفحة جميلة\n";
echo "   ✅ APIs محسنة للأداء\n\n";

echo "🔧 كيفية الاستخدام:\n";
echo "1. زيارة الصفحة الرئيسية - ستجد زر 'العملاء الجدد' في الأعلى\n";
echo "2. النقر على الزر - ستظهر قائمة جميلة بالعملاء الجدد\n";
echo "3. إضافة عميل جديد - ستحصل على إشعار فوري في:\n";
echo "   - سطح المكتب (إذا كان مفعلاً)\n";
echo "   - داخل الصفحة (إشعار أخضر في الأعلى)\n";
echo "   - صوت مميز (3 نغمات)\n";
echo "4. التحديث التلقائي كل 5 ثوان\n\n";

echo "💡 ميزات إضافية:\n";
echo "   - Ctrl+Shift+N: تفعيل/إلغاء تفعيل الإشعارات\n";
echo "   - النقر على الإشعار: الانتقال لصفحة العميل\n";
echo "   - تصميم متجاوب للهواتف\n";
echo "   - أصوات مختلفة حسب نوع الإشعار\n\n";

echo "🎉 تم الانتهاء من الاختبار!\n";
echo "🚀 النظام الجديد جاهز للاستخدام بسرعة فائقة!\n";

?>
