<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class estps extends Model
{


    use HasFactory;
    protected $guarded = [];




##########

    public function Hed_estpsmsm()
    {
        return $this->belongsTo('App\Models\hed_estps','estps_id');
    }

    #########################


    public function clients()
    {
        return $this->belongsTo(clients::class,'clients_id');

    }



}

