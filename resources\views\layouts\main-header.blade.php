<!-- main-header opened -->
<div class="main-header sticky side-header nav nav-item">
    <div class="container-fluid">
        <div class="main-header-left ">
            <div class="responsive-logo">

                <a href="{{ url('/' . ($page = 'index')) }}"><img src="{{ URL::asset('assets/img/brand/logo.png') }}"
                        class="logo-1" alt="logo"></a>
                <a href="{{ url('/' . ($page = 'index')) }}"><img
                        src="{{ URL::asset('assets/img/brand/logo-white.png') }}" class="dark-logo-1" alt="logo"></a>
                <a href="{{ url('/' . ($page = 'index')) }}"><img src="{{ URL::asset('assets/img/brand/favicon.png') }}"
                        class="logo-2" alt="logo"></a>
                <a href="{{ url('/' . ($page = 'index')) }}"><img src="{{ URL::asset('assets/img/brand/favicon.png') }}"
                        class="dark-logo-2" alt="logo"></a>
            </div>
            <div class="app-sidebar__toggle" data-toggle="sidebar">
                <a class="open-toggle" href="#"><i class="header-icon fe fe-align-left"></i></a>

                <a class="close-toggle" href="#"><i class="header-icons fe fe-x"></i></a>
            </div>

            <!--
      <div class="main-header-center mr-3 d-sm-none d-md-none d-lg-block">
       <input class="form-control" placeholder="Search for anything..." type="search"> <button class="btn"><i class="fas fa-search d-none d-md-block"></i></button>
      </div>
                    -->

            <h6>اهلا

                @php
                    $infocompanies = DB::table('infocompanies')->get();
                @endphp
                {{ $infocompanies->implode('nameofcompany') }}


                في تطبيق الويب الخاص بكم</h6>
        </div>

        <div class="main-header-right">

            <ul class="nav">

                <li class="">

                    <div class="dropdown  nav-itemd-none d-md-flex">
                        <!--
<a href="#" class="d-flex  nav-item nav-link pl-0 country-flag1" data-toggle="dropdown" aria-expanded="false">
  <span class="avatar country-Flag mr-0 align-self-center bg-transparent"><img src="{{ URL::asset('assets/img/flags/us_flag.jpg') }}" alt="img"></span>
  <div class="my-auto">
   <strong class="mr-2 ml-2 my-auto">English</strong>
  </div>

 </a>
-->
                        <button onClick="window.location.reload();" type="submit" alt="تحديث"
                            style="background-color:transparent; border-color:transparent;">
                            <img src="{{ URL::asset('assets/icons/reload-page.png') }}" title="تحديث"
                                height="25" />
                        </button>






                        <div class="dropdown-menu dropdown-menu-left dropdown-menu-arrow" x-placement="bottom-end">
                            <a href="#" class="dropdown-item d-flex ">
                                <span class="avatar  ml-3 align-self-center bg-transparent"><img
                                        src="{{ URL::asset('assets/img/flags/french_flag.jpg') }}"
                                        alt="img"></span>
                                <div class="d-flex">
                                    <span class="mt-2">French</span>
                                </div>
                            </a>
                            <a href="#" class="dropdown-item d-flex">
                                <span class="avatar  ml-3 align-self-center bg-transparent"><img
                                        src="{{ URL::asset('assets/img/flags/germany_flag.jpg') }}"
                                        alt="img"></span>
                                <div class="d-flex">
                                    <span class="mt-2">Germany</span>
                                </div>
                            </a>
                            <a href="#" class="dropdown-item d-flex">
                                <span class="avatar ml-3 align-self-center bg-transparent"><img
                                        src="{{ URL::asset('assets/img/flags/italy_flag.jpg') }}"
                                        alt="img"></span>
                                <div class="d-flex">
                                    <span class="mt-2">Italy</span>
                                </div>
                            </a>
                            <a href="#" class="dropdown-item d-flex">
                                <span class="avatar ml-3 align-self-center bg-transparent"><img
                                        src="{{ URL::asset('assets/img/flags/russia_flag.jpg') }}"
                                        alt="img"></span>
                                <div class="d-flex">
                                    <span class="mt-2">Russia</span>
                                </div>
                            </a>
                            <a href="#" class="dropdown-item d-flex">
                                <span class="avatar  ml-3 align-self-center bg-transparent"><img
                                        src="{{ URL::asset('assets/img/flags/spain_flag.jpg') }}"
                                        alt="img"></span>
                                <div class="d-flex">
                                    <span class="mt-2">spain</span>
                                </div>


                            </a>

                        </div>
                    </div>
                </li>
            </ul>

            <div class="nav nav-item  navbar-nav-right ml-auto">
                <div class="nav-link" id="bs-example-navbar-collapse-1">
                    <form class="navbar-form" role="search">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search">
                            <span class="input-group-btn">
                                <button type="reset" class="btn btn-default">
                                    <i class="fas fa-times"></i>
                                </button>
                                <button type="submit" class="btn btn-default nav-link resp-btn">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="header-icon-svgs"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round"
                                        class="feather feather-search">
                                        <circle cx="11" cy="11" r="8"></circle>
                                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                                    </svg>
                                </button>
                            </span>
                        </div>
                    </form>
                </div>
                <div class="dropdown nav-item main-header-message ">
                    <!--
<a class="new nav-link" href="#"><svg xmlns="http://www.w3.org/2000/svg" class="header-icon-svgs" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-mail"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg><span class=" pulse-danger"></span></a>
                    -->
                    <div class="dropdown-menu">
                        <div class="menu-header-content bg-primary text-right">


                            <div class="d-flex">
                                <h6 class="dropdown-title mb-1 tx-15 text-white font-weight-semibold">Messages</h6>
                                <span class="badge badge-pill badge-warning mr-auto my-auto float-left">Mark All
                                    Read</span>
                            </div>
                            <p class="dropdown-title-text subtext mb-0 text-white op-6 pb-0 tx-12 ">You have 4 unread
                                messages</p>
                        </div>
                        <div class="main-message-list chat-scroll">

                            <a href="#" class="p-3 d-flex border-bottom">

                                <div class="drop-img  cover-image"
                                    data-image-src="{{ URL::asset('assets/img/faces/3.jpg') }}">
                                    <span class="avatar-status bg-teal"></span>
                                </div>
                                <div class="wd-90p">
                                    <div class="d-flex">
                                        <h5 class="mb-1 name">Petey Cruiser</h5>
                                    </div>
                                    <p class="mb-0 desc">I'm sorry but i'm not sure how to help you with that......</p>
                                    <p class="time mb-0 text-left float-right mr-2 mt-2">Mar 15 3:55 PM</p>
                                </div>
                            </a>
                            <a href="#" class="p-3 d-flex border-bottom">
                                <div class="drop-img cover-image"
                                    data-image-src="{{ URL::asset('assets/img/faces/2.jpg') }}">
                                    <span class="avatar-status bg-teal"></span>
                                </div>
                                <div class="wd-90p">
                                    <div class="d-flex">
                                        <h5 class="mb-1 name">Jimmy Changa</h5>
                                    </div>
                                    <p class="mb-0 desc">All set ! Now, time to get to you now......</p>
                                    <p class="time mb-0 text-left float-right mr-2 mt-2">Mar 06 01:12 AM</p>
                                </div>
                            </a>
                            <a href="#" class="p-3 d-flex border-bottom">
                                <div class="drop-img cover-image"
                                    data-image-src="{{ URL::asset('assets/img/faces/9.jpg') }}">
                                    <span class="avatar-status bg-teal"></span>
                                </div>
                                <div class="wd-90p">
                                    <div class="d-flex">
                                        <h5 class="mb-1 name">Graham Cracker</h5>
                                    </div>
                                    <p class="mb-0 desc">Are you ready to pickup your Delivery...</p>
                                    <p class="time mb-0 text-left float-right mr-2 mt-2">Feb 25 10:35 AM</p>
                                </div>
                            </a>
                            <a href="#" class="p-3 d-flex border-bottom">
                                <div class="drop-img cover-image"
                                    data-image-src="{{ URL::asset('assets/img/faces/12.jpg') }}">
                                    <span class="avatar-status bg-teal"></span>
                                </div>
                                <div class="wd-90p">
                                    <div class="d-flex">
                                        <h5 class="mb-1 name">Donatella Nobatti</h5>
                                    </div>
                                    <p class="mb-0 desc">Here are some products ...</p>
                                    <p class="time mb-0 text-left float-right mr-2 mt-2">Feb 12 05:12 PM</p>
                                </div>
                            </a>
                            <a href="#" class="p-3 d-flex border-bottom">
                                <div class="drop-img cover-image"
                                    data-image-src="{{ URL::asset('assets/img/faces/5.jpg') }}">
                                    <span class="avatar-status bg-teal"></span>
                                </div>
                                <div class="wd-90p">
                                    <div class="d-flex">
                                        <h5 class="mb-1 name">Anne Fibbiyon</h5>
                                    </div>
                                    <p class="mb-0 desc">I'm sorry but i'm not sure how...</p>
                                    <p class="time mb-0 text-left float-right mr-2 mt-2">Jan 29 03:16 PM</p>
                                </div>
                            </a>
                        </div>
                        <div class="text-center dropdown-footer">
                            <a href="text-center">VIEW ALL</a>
                        </div>
                    </div>
                </div>
                <!--
<div class="dropdown nav-item main-header-notification">
<a class="new nav-link notification-bell" href="#" id="notificationDropdown" data-toggle="dropdown">
<div class="notification-icon-wrapper">
<svg xmlns="http://www.w3.org/2000/svg" class="header-icon-svgs notification-bell-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
<path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
<path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
</svg>
<span class="notification-count-badge" id="notificationCount" style="display: none;">0</span>
<div class="notification-pulse" id="notificationPulse" style="display: none;"></div>
</div>
</a>
<div class="dropdown-menu notification-dropdown" style="width: 380px; max-height: 450px; overflow-y: auto; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
 <div class="notification-header">
  <div class="d-flex justify-content-between align-items-center">
   <h6 class="notification-title">🔔 الإشعارات</h6>
   <div class="notification-actions">
    <button class="btn btn-sm btn-outline-light" id="markAllRead" title="تحديد الكل كمقروء">
     <i class="fa fa-check-double"></i>
    </button>
    <button class="btn btn-sm btn-outline-light" id="refreshNotifications" title="تحديث">
     <i class="fa fa-sync"></i>
    </button>
   </div>
  </div>
  <p class="notification-subtitle" id="notificationSubtext">لا توجد إشعارات جديدة</p>
 </div>
 <div class="notification-list-container" id="notificationList">
  <a class="d-flex p-3 border-bottom" href="#">
   <div class="notifyimg bg-pink">
    <i class="la la-file-alt text-white"></i>
   </div>
   <div class="mr-3">
    <h5 class="notification-label mb-1">New files available</h5>
    <div class="notification-subtext">10 hour ago</div>
   </div>
   <div class="mr-auto" >
    <i class="las la-angle-left text-left text-muted"></i>
   </div>
  </a>
  <a class="d-flex p-3" href="#">
   <div class="notifyimg bg-purple">
    <i class="la la-gem text-white"></i>
   </div>
   <div class="mr-3">
    <h5 class="notification-label mb-1">Updates Available</h5>
    <div class="notification-subtext">2 days ago</div>
   </div>
   <div class="mr-auto" >
    <i class="las la-angle-left text-left text-muted"></i>
   </div>
  </a>
  <a class="d-flex p-3 border-bottom" href="#">
   <div class="notifyimg bg-success">
    <i class="la la-shopping-basket text-white"></i>
   </div>
   <div class="mr-3">
    <h5 class="notification-label mb-1">New Order Received</h5>
    <div class="notification-subtext">1 hour ago</div>
   </div>
   <div class="mr-auto" >
    <i class="las la-angle-left text-left text-muted"></i>
   </div>
  </a>
  <a class="d-flex p-3 border-bottom" href="#">
   <div class="notifyimg bg-warning">
    <i class="la la-envelope-open text-white"></i>
   </div>
   <div class="mr-3">
    <h5 class="notification-label mb-1">New review received</h5>
    <div class="notification-subtext">1 day ago</div>
   </div>
   <div class="mr-auto" >
    <i class="las la-angle-left text-left text-muted"></i>
   </div>
  </a>
  <a class="d-flex p-3 border-bottom" href="#">
   <div class="notifyimg bg-danger">
    <i class="la la-user-check text-white"></i>
   </div>
   <div class="mr-3">
    <h5 class="notification-label mb-1">22 verified registrations</h5>
    <div class="notification-subtext">2 hour ago</div>
   </div>
   <div class="mr-auto" >
    <i class="las la-angle-left text-left text-muted"></i>
   </div>
  </a>
  <a class="d-flex p-3 border-bottom" href="#">
   <div class="notifyimg bg-primary">
    <i class="la la-check-circle text-white"></i>
   </div>
   <div class="mr-3">
    <h5 class="notification-label mb-1">Project has been approved</h5>
    <div class="notification-subtext">4 hour ago</div>
   </div>
   <div class="mr-auto" >
    <i class="las la-angle-left text-left text-muted"></i>
   </div>
  </a>
 </div>
 <div class="notification-footer">
  <a href="{{ route('notifications.index') }}" class="btn btn-primary btn-sm">
   <i class="fa fa-list me-1"></i>
   عرض الكل
  </a>
  <a href="{{ route('notifications.settings') }}" class="btn btn-secondary btn-sm">
   <i class="fa fa-cog me-1"></i>
   الإعدادات
  </a>
  <button class="btn btn-warning btn-sm" id="testNotificationBtn">
   <i class="fa fa-flask me-1"></i>
   اختبار
  </button>
 </div>
</div>
</div>
<div class="nav-item full-screen fullscreen-button">
<a class="new nav-link full-screen-link" href="#"><svg xmlns="http://www.w3.org/2000/svg" class="header-icon-svgs" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-maximize"><path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path></svg></a>

                    </div>







                -->





                <div class="dropdown main-profile-menu nav nav-item nav-link">
                    <a class="profile-user d-flex" title="المدير" href=""><img alt=""
                            src="{{ URL::asset('assets/img/faces/6.jpg') }}"></a>
                    <div class="dropdown-menu">
                        <div class="main-header-profile bg-primary p-3">
                            <div class="d-flex wd-100p">
                                <div class="main-img-user"><img alt="المدير" title="المدير"
                                        src="{{ URL::asset('assets/img/faces/6.jpg') }}" class=""></div>
                                <div class="mr-3 my-auto">


                                    <h6>{{ Auth::user()->name }}</h6><span>{{ Auth::user()->email }}</span>











                                </div>
                            </div>
                        </div>



                        <a class="dropdown-item" href="{{ url('profile') }}"
                            onclick="event.preventDefault();document.getElementById('alt-form').submit();"><i
                                class="bx bx-edit-alt"></i> التعديل </a>
                        <form id="alt-form" action="{{ url('profile') }}" style="display: none;">
                            @csrf
                        </form>


                        <a class="dropdown-item" href="{{ route('logout') }}"
                            onclick="event.preventDefault();document.getElementById('logout-form').submit();"><i
                                class="bx bx-log-out"></i>تسجيل خروج</a>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST"
                            style="display: none;">
                            @csrf

                        </form>









                    </div>
                </div>
                <div class="dropdown main-header-message right-toggle">
                    <!--
<a class="nav-link pr-0" data-toggle="sidebar-left" data-target=".sidebar-left">
 <svg xmlns="http://www.w3.org/2000/svg" class="header-icon-svgs" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-menu"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg>
</a>
                    -->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /main-header -->
