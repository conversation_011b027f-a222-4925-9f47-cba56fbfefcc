<?php

namespace App\Http\Controllers;


use App\Models\ques;
use App\Models\estps;
use App\Models\moneys;
use App\Models\visits;
use App\Models\clients;
use App\Models\hed_estps;
use App\Models\challenges;
use App\Models\historycls;
use Illuminate\Http\Request;
use App\Models\subscriptions;
use App\Models\clientschallenges;
use App\Models\clientssubscriptions;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Facade;



class EstpsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $ques = ques::all();
        $estps = estps::all();





        return view('estps.estps', compact('ques','estps'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {


        $validatedData = $request->validate([
            'question1' => 'required:estps|max:255',
        ],[
            'question1.required' =>'يرجي ادخال السؤال',
        ]);

        $createhed_estps=hed_estps::create([
            'clients_id' => $request->id,
            'datenow' => date('Y-m-d'),

            // 'created_at' => (Auth::user()->name),
            //  'updated_at' => (Auth::user()->name),
        ]  );



        $ques = ques::all();

        $ttt=count($ques);

        for($j=1;$j<=$ttt;$j++)
        {


        $createestps=estps::create(array(
            'estps_id' => $createhed_estps->id,
            'question' => $request->string("question$j"),
            'answer' => $request->string("answer$j"),
            'note' => $request->string("note$j"),
            'created_at_user' => Auth::user()->name,




        ));



        }



        session()->flash('Add', 'تم اضافة الاستبيان بنجاح ');
        return redirect('/clients');


    }

    /**
     * Display the specified resource.
     */
    public function show(estps $estps)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        //قرائة معلومات الزبون من الاي دي
        $clientssss = clients::where('id',$id)->first();

        //سحب الاي دي الخاص براس الاستبيان من الاي بي الخاص بالزبون
        $hed_estpsaa = hed_estps::where('clients_id',$id)->first();

        $dddatadd= $hed_estpsaa->id; //1/16

        //سحب المعلومات الخاصة براس الاستبيان الخاص بالزبون
        $hed_estpsss = hed_estps::where('id',$dddatadd)->first();


        //سحب الاي دي الخاص بالزبون لتعريفة في جدول الزبائن لاخذ الاسم
        $dddataddsssss= $hed_estpsss->clients_id;

        //جميع معلومات الزبون
        //$clients=DB::table('clients')->where(array('id' => $dddataddsssss))->first();
        $clients = clients::where('id',$dddataddsssss)->first();



        $estpsssssd = estps::where('estps_id',$dddatadd)->get();
        $clientsss= $clients->name;


        $clients = clients::where('id',$dddataddsssss)->first();
        $nameofclient=$clients ->name;



        $estps = estps::where('estps_id',$dddatadd)->get();

        //$estps=DB::table('estps')->where(array('estps_id' => $dddatadd))->get();

        $challenges = challenges::all();


        $clientschallenges = clientschallenges::where('clients_id',$id)->get();





        $visits = visits::where('clients_id',$id)->get();

        $subscriptions = subscriptions::all();
        $clientssubscriptions = clientssubscriptions::all();





 // $money = money::where('clients_id',$id)->get()->sortByDesc("id");

 $moneys = moneys::where('clients_id',$id)->get()->sortBy('dateofmoney');

 $historycls = historycls::where('clients_id',$id)->get()->sortBy('created_at');

        return view('estps.estpsdetals', compact('clients','estps','challenges','clientschallenges','visits','subscriptions','clientssubscriptions','moneys','historycls'));


    }




    public function editnoe($id)
    {
        //قرائة معلومات الزبون من الاي دي
        $clientssss = clients::where('id',$id)->first();






        //جميع معلومات الزبون
        //$clients=DB::table('clients')->where(array('id' => $id))->first();
        $clients = clients::where('id',$id)->first();









        //$estps=DB::table('estps')->where(array('estps_id' => $dddatadd))->get();

        $challenges = challenges::all();


        $clientschallenges = clientschallenges::where('clients_id',$id)->get();





        $visits = visits::where('clients_id',$id)->get();

        $subscriptions = subscriptions::all();
        $clientssubscriptions = clientssubscriptions::all();





 // $money = money::where('clients_id',$id)->get()->sortByDesc("id");

 $moneys = moneys::where('clients_id',$id)->get()->sortBy('dateofmoney');




 $historycls = historycls::where('clients_id',$id)->get()->sortBy('created_at');



        return view('estps.estpsdetalsnoe', compact('clients','challenges','clientschallenges','visits','subscriptions','clientssubscriptions','moneys','historycls'));


    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $id = $request->id;
        $estps_id = $request->estps_id;



        $validate = $request->validate([


            'question' => 'required:estps,question,'.$id,


        ],[

            'answer.required' =>'يرجي ادخال الجواب',


        ]);

        $estps = estps::find($id);
        $estps->update([
            'answer' => $request->answer,
            'note' => $request->note,






        ]);



        session()->flash('edit','تم تعديل السؤال بنجاج');
        //return redirect('/estpsdetals/$clients ->id');
        return redirect()->back();


        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(estps $estps)
    {
        //
    }
}
