<?php

namespace App\Http\Controllers;

use App\Models\ques;
use App\Models\estps;
use App\Models\hed_estps;
use App\Models\clients;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ClientsController extends Controller
{




    /**
    /**
     * Display a listing of the resource.
     */
    public function index()
    {


        $clients = clients::all()->sortByDesc("id");
        $ques = ques::all();
        //$hed_estps = hed_estps::all();


        return view('clients.clients', compact('clients','ques'));


        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {


        $validatedData = $request->validate([
            'name' => 'required|unique:clients|max:255',
            'phone' => 'required|unique:clients|max:255|digits:9|regex:/[0-9]{9}/',
            //'password' => 'required:clients|max:255|min:6',
            'roles_name' => 'required:clients|max:255',
            'active' => 'required:clients|max:255',
            'status' => 'required:clients|max:255',

    ],[

        'name.required' =>'يرجي ادخال اسم الزبون',
        'name.unique' =>'اسم الزبون مسجل مسبقا',

        'phone.regex' =>'يجب ان يكون الهاتف عبارة عن ارقام فقط',
        'phone.digits' =>'عدد ارقام الهاتف خاطيء',

        'phone.unique' =>'الهاتف مسجل مسبقا',

        //'password.min' =>'كلمة السر قصيرة جدا يجب ان تكون اكبر من 6 خانات',

    ]);

    clients::create([
        'name' => $request->name,
        'gender' => $request->gender,
        'phone' => $request->phone,
        'subscroptiondate' => $request->subscroptiondate,
        'note' => $request->note,
        'password' => $request->phone,
        'roles_name' => $request->roles_name,
        'active' => $request->active,
        'status' => $request->status,





       // 'created_at' => (Auth::user()->name),
      //  'updated_at' => (Auth::user()->name),
    ]);
    session()->flash('Add', 'تم اضافة الزبون بنجاح ');
    return redirect('/clients');


}



    /**
     * Display the specified resource.
     */
    public function show(clients $clients)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(clients $clients)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $id = $request->id;


        $validatedData = $request->validate([


            'name' => 'required|max:255|unique:clients,name,'.$id,

            'phone' => 'required:clients|max:255|digits:9|regex:/[0-9]{9}/',

            'note' => 'required:clients,'.$id,
            'password' => 'required:clients|max:255|min:6',
            'roles_name' => 'required:clients|max:255',
            'active' => 'required:clients|max:255',
            'status' => 'required:clients|max:255',


        ],[

            'name.required' =>'يرجي ادخال اسم الزبون',
            'name.unique' =>'اسم الزبون مسجل مسبقا',

            'phone.regex' =>'يجب ان يكون الهاتف عبارة عن ارقام فقط',
            'phone.digits' =>'عدد ارقام الهاتف خاطيء',

            'phone.unique' =>'رقم الهاتف او الموبايل مسجل مسبقا',

            'password.min' =>'كلمة السر قصيرة جدا يجب ان تكون اكبر من 6 خانات',


        ]);

        $clients = clients::find($id);
        $clients->update([
            'name' => $request->name,
            'gender' => $request->gender,
            'phone' => $request->phone,
            'subscroptiondate' => $request->subscroptiondate,
            'note' => $request->note,
            'password' => $request->password,
            'roles_name' => $request->roles_name,
            'active' => $request->active,
            'status' => $request->status,












        ]);

        session()->flash('edit','تم تعديل الزبون بنجاج');
        return redirect('/clients');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {
        $id = $request->id;
        clients::find($id)->delete();
        session()->flash('delete','تم حذف الاسم بنجاح');
        return redirect()->back();

        //
    }
}
