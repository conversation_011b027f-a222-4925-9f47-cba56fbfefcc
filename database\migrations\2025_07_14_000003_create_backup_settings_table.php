<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backup_settings', function (Blueprint $table) {
            $table->id();
            $table->boolean('auto_backup_enabled')->default(true); // تفعيل النسخ التلقائي
            $table->boolean('daily_backup')->default(true); // نسخ يومي
            $table->boolean('weekly_backup')->default(true); // نسخ أسبوعي
            $table->boolean('monthly_backup')->default(true); // نسخ شهري
            $table->string('backup_path')->default('storage/backups'); // مسار النسخ الاحتياطي
            $table->string('backup_url')->nullable(); // رابط النسخ الاحتياطي الخارجي
            $table->integer('keep_backups_days')->default(30); // عدد الأيام للاحتفاظ بالنسخ
            $table->boolean('compress_backups')->default(true); // ضغط النسخ الاحتياطي
            $table->json('backup_tables')->nullable(); // الجداول المراد نسخها
            $table->string('backup_time')->default('02:00'); // وقت النسخ الاحتياطي
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_settings');
    }
};
