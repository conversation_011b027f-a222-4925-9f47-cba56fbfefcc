<?php

namespace App\Http\Controllers;

use App\Models\challenges;
use Illuminate\Http\Request;

class ChallengesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $challenges = challenges::all();
        return view('challenges.challenges',compact('challenges'));

        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'tahaddename' => 'required|unique:challenges|max:255',
        ],[

            'tahaddename.required' =>'يرجي ادخال اسم التحدي',
            'tahaddename.unique' =>'اسم التحدي مسجل مسبقا',


        ]);

        challenges::create([
        'tahaddename' => $request->tahaddename,
        'startdate' => $request->startdate,
        'timeenddate' => $request->  timeenddate,



        ]);
        session()->flash('Add', 'تم اضافة القسم بنجاح ');
        return redirect()->back();


    }



    /**
     * Display the specified resource.
     */
    public function show(challenges $challenges)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(challenges $challenges)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)

        {

            $id = $request->id;

            $validate = $request->validate([


                'tahaddename' => 'required|max:255|unique:challenges,tahaddename,'.$id,
            ],[

                'tahaddename.required' =>'يرجي ادخال اسم التحدي',
                'tahaddename.unique' =>'اسم التحدي مسجل مسبقا',

            ]);

            $challenges = challenges::find($id);
            $challenges->update([
                'tahaddename' => $request->tahaddename,
                'startdate' => $request->startdate,
                'timeenddate' => $request->timeenddate,



            ]);

            session()->flash('edit','تم تعديل النحدي بنجاج');
            return redirect()->back();
        }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {

        $id = $request->id;
        challenges::find($id)->delete();
        session()->flash('delete','تم حذف التحدي بنجاح');
        return redirect()->back();
        //
    }
}
