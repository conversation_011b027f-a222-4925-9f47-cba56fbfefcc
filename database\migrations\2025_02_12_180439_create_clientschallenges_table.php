<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clientschallenges', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clients_id');
            $table->unsignedBigInteger('tahadde_id');
            $table->date('startdateofcustomerchallenges');
            $table->integer('endtdateofcustomerchallenges');
            $table->foreign('clients_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('tahadde_id')->references('id')->on('challenges');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clientschallenges');
    }
};
