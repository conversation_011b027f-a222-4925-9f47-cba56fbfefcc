<?php

/**
 * اختبار Observer للعملاء
 * تشغيل: php test_client_observer.php
 */

echo "🧪 اختبار Observer للعملاء...\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// تحميل Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل Laravel: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. فحص تسجيل Observer
echo "1️⃣ فحص تسجيل Observer...\n";
try {
    $providerContent = file_get_contents('app/Providers/AppServiceProvider.php');
    if (strpos($providerContent, 'clients::observe(ClientObserver::class)') !== false) {
        echo "✅ Observer مسجل في AppServiceProvider\n";
    } else {
        echo "❌ Observer غير مسجل في AppServiceProvider\n";
    }
    
    if (file_exists('app/Observers/ClientObserver.php')) {
        echo "✅ ملف ClientObserver موجود\n";
    } else {
        echo "❌ ملف ClientObserver غير موجود\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص Observer: " . $e->getMessage() . "\n";
}
echo "\n";

// 2. فحص النماذج المطلوبة
echo "2️⃣ فحص النماذج...\n";
try {
    $clientsModel = new App\Models\clients();
    echo "✅ نموذج clients يعمل\n";
    
    $notificationModel = new App\Models\Notification();
    echo "✅ نموذج Notification يعمل\n";
    
    $userModel = new App\Models\User();
    echo "✅ نموذج User يعمل\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في النماذج: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. فحص المستخدمين المستهدفين
echo "3️⃣ فحص المستخدمين المستهدفين...\n";
try {
    $users = App\Models\User::whereIn('role', ['super', 'admin', 'secretary'])->get();
    echo "✅ عدد المستخدمين المستهدفين: " . $users->count() . "\n";
    
    foreach ($users as $user) {
        echo "   - {$user->name} ({$user->role})\n";
    }
    
    if ($users->count() === 0) {
        echo "⚠️  لا يوجد مستخدمين مستهدفين. تأكد من وجود مستخدمين بأدوار: super, admin, secretary\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص المستخدمين: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. إنشاء عميل تجريبي
echo "4️⃣ إنشاء عميل تجريبي...\n";
try {
    // تسجيل دخول مستخدم تجريبي
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        echo "✅ تم تسجيل دخول المستخدم: {$testUser->name}\n";
    }
    
    // إنشاء عميل تجريبي
    $testClient = App\Models\clients::create([
        'name' => 'عميل تجريبي - ' . date('Y-m-d H:i:s'),
        'gender' => 'male',
        'phone' => '59' . rand(1000000, 9999999),
        'subscroptiondate' => now()->toDateString(),
        'note' => 'عميل تجريبي لاختبار Observer',
        'password' => '123456',
        'roles_name' => 'yes',
        'active' => 'yes',
        'status' => 'active'
    ]);
    
    echo "✅ تم إنشاء عميل تجريبي: {$testClient->name} (ID: {$testClient->id})\n";
    
    // انتظار قليل للسماح للـ Observer بالعمل
    sleep(2);
    
    // فحص الإشعارات المُنشأة
    $notifications = App\Models\Notification::where('type', 'new_client')
                                          ->where('related_id', $testClient->id)
                                          ->get();
    
    echo "✅ عدد الإشعارات المُنشأة: " . $notifications->count() . "\n";
    
    foreach ($notifications as $notification) {
        $user = App\Models\User::find($notification->user_id);
        echo "   - إشعار للمستخدم: {$user->name} - {$notification->title}\n";
    }
    
    if ($notifications->count() === 0) {
        echo "❌ لم يتم إنشاء أي إشعارات! Observer قد لا يعمل بشكل صحيح\n";
        
        // فحص اللوج
        echo "\n📋 فحص آخر سجلات اللوج:\n";
        $logFile = 'storage/logs/laravel.log';
        if (file_exists($logFile)) {
            $logContent = file_get_contents($logFile);
            $lines = explode("\n", $logContent);
            $recentLines = array_slice($lines, -20);
            
            foreach ($recentLines as $line) {
                if (strpos($line, 'ClientObserver') !== false) {
                    echo "   " . $line . "\n";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء العميل التجريبي: " . $e->getMessage() . "\n";
    echo "تفاصيل الخطأ: " . $e->getTraceAsString() . "\n";
}
echo "\n";

// 5. تنظيف البيانات التجريبية
echo "5️⃣ تنظيف البيانات التجريبية...\n";
try {
    if (isset($testClient)) {
        // حذف الإشعارات المرتبطة
        App\Models\Notification::where('related_id', $testClient->id)
                               ->where('related_type', 'App\Models\clients')
                               ->delete();
        
        // حذف العميل التجريبي
        $testClient->delete();
        echo "✅ تم حذف البيانات التجريبية\n";
    }
} catch (Exception $e) {
    echo "⚠️  تحذير في التنظيف: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. التوصيات
echo "💡 التوصيات:\n";
echo "=" . str_repeat("=", 15) . "\n";

if (!isset($notifications) || $notifications->count() === 0) {
    echo "❌ Observer لا يعمل بشكل صحيح. جرب:\n";
    echo "   1. تشغيل: php artisan config:clear\n";
    echo "   2. تشغيل: composer dump-autoload\n";
    echo "   3. إعادة تشغيل الخادم\n";
    echo "   4. فحص storage/logs/laravel.log للأخطاء\n";
} else {
    echo "✅ Observer يعمل بشكل صحيح!\n";
    echo "   - جرب إضافة عميل جديد من الواجهة\n";
    echo "   - تحقق من الإشعارات في /notifications\n";
    echo "   - تأكد من تفعيل إشعارات سطح المكتب\n";
}

echo "\n🎯 للاختبار الحقيقي:\n";
echo "1. اذهب إلى /clients\n";
echo "2. أضف عميل جديد\n";
echo "3. تحقق من الإشعارات في /notifications\n";
echo "4. تحقق من إشعارات سطح المكتب\n";

echo "\n🎉 انتهى اختبار Observer!\n";

?>
