<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BackupSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'auto_backup_enabled',
        'daily_backup',
        'weekly_backup',
        'monthly_backup',
        'backup_path',
        'backup_url',
        'keep_backups_days',
        'compress_backups',
        'backup_tables',
        'backup_time'
    ];

    protected $casts = [
        'auto_backup_enabled' => 'boolean',
        'daily_backup' => 'boolean',
        'weekly_backup' => 'boolean',
        'monthly_backup' => 'boolean',
        'compress_backups' => 'boolean',
        'backup_tables' => 'array'
    ];

    /**
     * الحصول على الإعدادات أو إنشاء إعدادات افتراضية
     */
    public static function getSettings()
    {
        return static::firstOrCreate(
            ['id' => 1],
            [
                'auto_backup_enabled' => true,
                'daily_backup' => true,
                'weekly_backup' => true,
                'monthly_backup' => true,
                'backup_path' => 'storage/backups',
                'keep_backups_days' => 30,
                'compress_backups' => true,
                'backup_time' => '02:00',
                'backup_tables' => [
                    'users',
                    'clients',
                    'visits',
                    'moneys',
                    'challenges',
                    'subscriptions',
                    'estps',
                    'hed_estps',
                    'clientschallenges',
                    'clientssubscriptions',
                    'historycls',
                    'infocompanies',
                    'ques'
                ]
            ]
        );
    }

    /**
     * تحديث الإعدادات
     */
    public static function updateSettings($data)
    {
        $settings = static::getSettings();
        $settings->update($data);
        return $settings;
    }

    /**
     * الحصول على الجداول المتاحة للنسخ الاحتياطي
     */
    public static function getAvailableTables()
    {
        return [
            'users' => 'المستخدمين',
            'clients' => 'العملاء',
            'visits' => 'الزيارات',
            'moneys' => 'المالية',
            'challenges' => 'التحديات',
            'subscriptions' => 'الاشتراكات',
            'estps' => 'الاستبيانات',
            'hed_estps' => 'رؤوس الاستبيانات',
            'clientschallenges' => 'تحديات العملاء',
            'clientssubscriptions' => 'اشتراكات العملاء',
            'historycls' => 'سجل العملاء',
            'infocompanies' => 'معلومات الشركة',
            'ques' => 'الأسئلة',
            'notifications' => 'الإشعارات',
            'notification_settings' => 'إعدادات الإشعارات'
        ];
    }
}
