# 🔊 دليل الأصوات - نظام الإشعارات

## الملفات المطلوبة:

### 1. notification.mp3 ✅
- الصوت الافتراضي للإشعارات
- نغمة هادئة ولطيفة

### 2. bell.mp3
- صوت جرس كلاسيكي
- للإشعارات المهمة

### 3. chime.mp3
- نغمة موسيقية جميلة
- للإشعارات الإيجابية

### 4. ding.mp3
- صوت دينغ قصير
- للتنبيهات السريعة

### 5. pop.mp3
- صوت بوب خفيف
- للإشعارات العادية

### 6. swoosh.mp3
- صوت سووش ناعم
- للإشعارات الناعمة

## 🎵 مصادر الأصوات المجانية:

1. **Freesound.org** - مكتبة ضخمة من الأصوات المجانية
2. **Pixabay** - أصوات عالية الجودة
3. **Zapsplat** - مكتبة احترافية
4. **YouTube Audio Library** - مكتبة يوتيوب

## ⚙️ المتطلبات التقنية:

- **التنسيق**: MP3
- **الجودة**: 128kbps أو أعلى
- **المدة**: 0.5-3 ثواني
- **الحجم**: أقل من 100KB
- **التردد**: 44.1kHz

## 🚀 كيفية الاستخدام:

1. حمل الملفات الصوتية بالأسماء المحددة
2. ضعها في مجلد `public/assets/sounds/`
3. اختبر الأصوات من صفحة الإعدادات
4. اضبط مستوى الصوت حسب الحاجة

## 🔧 استكشاف الأخطاء:

### إذا لم يعمل الصوت:
1. تأكد من وجود الملف في المجلد الصحيح
2. تحقق من صيغة الملف (MP3)
3. تأكد من أن المتصفح يدعم تشغيل الصوت
4. فحص إعدادات الصوت في المتصفح

### إذا كان الصوت عالياً جداً:
1. اضبط مستوى الصوت من الإعدادات
2. استخدم برنامج تحرير صوتي لتقليل الصوت
3. اختر ملف صوتي أهدأ

## 📝 ملاحظات مهمة:

- تأكد من أن الأصوات غير محمية بحقوق طبع ونشر
- اختبر الأصوات على متصفحات مختلفة
- احتفظ بنسخة احتياطية من الأصوات
- يمكن إضافة أصوات جديدة بتحديث الكود

## 🎯 نصائح للحصول على أفضل تجربة:

1. **اختر أصواتاً متناسقة** - نفس النوعية والجودة
2. **تجنب الأصوات المزعجة** - خاصة في بيئة العمل
3. **اختبر مع الفريق** - تأكد من أن الجميع مرتاح للأصوات
4. **وفر خيارات متنوعة** - ليختار كل مستخدم ما يناسبه

---

💡 **نصيحة**: ابدأ بأصوات هادئة وقصيرة، يمكن دائماً تغييرها لاحقاً!
