<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // النسخ الاحتياطي اليومي
        $schedule->command('backup:daily')
                 ->dailyAt('02:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // النسخ الاحتياطي الأسبوعي (كل يوم جمعة)
        $schedule->command('backup:weekly')
                 ->weeklyOn(5, '03:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // النسخ الاحتياطي الشهري (أول يوم في الشهر)
        $schedule->command('backup:monthly')
                 ->monthlyOn(1, '04:00')
                 ->withoutOverlapping()
                 ->runInBackground();

        // حذف الإشعارات القديمة (كل يوم)
        $schedule->call(function () {
            \App\Models\Notification::deleteOldNotifications(30);
        })->daily();

        // حذف النسخ الاحتياطية القديمة (كل أسبوع)
        $schedule->call(function () {
            $settings = \App\Models\BackupSetting::getSettings();
            \App\Models\BackupLog::deleteOldBackups($settings->keep_backups_days);
        })->weekly();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
