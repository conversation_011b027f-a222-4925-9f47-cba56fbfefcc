/* تصميم نظام الإشعارات المتقدم */

/* تصميم أيقونة الجرس */
.notification-bell {
    position: relative;
    transition: all 0.3s ease;
}

.notification-bell:hover {
    transform: scale(1.1);
}

.notification-icon-wrapper {
    position: relative;
    display: inline-block;
}

.notification-bell-icon {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
}

.notification-bell:hover .notification-bell-icon {
    animation: bellRing 0.5s ease-in-out;
    color: #007bff;
}

/* عداد الإشعارات */
.notification-count-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(45deg, #ff4757, #ff3838);
    color: white;
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    font-size: 11px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
    animation: countPulse 2s infinite;
}

/* نبضة الإشعارات الجديدة */
.notification-pulse {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 12px;
    height: 12px;
    background: #ff4757;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

/* تصميم القائمة المنسدلة */
.notification-dropdown {
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
    margin-top: 10px;
}

.notification-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
}

.notification-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.notification-subtitle {
    margin: 5px 0 0 0;
    font-size: 12px;
    opacity: 0.8;
}

.notification-actions button {
    margin-left: 5px;
    border-radius: 20px;
    padding: 5px 10px;
    transition: all 0.3s ease;
}

.notification-actions button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* قائمة الإشعارات */
.notification-list-container {
    max-height: 350px;
    overflow-y: auto;
    background: white;
}

.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    display: block;
    position: relative;
}

.notification-item:hover {
    background: #f8f9ff;
    transform: translateX(-3px);
    text-decoration: none;
    color: inherit;
}

.notification-item.unread {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, rgba(255, 255, 255, 1) 20%);
    border-left: 4px solid #667eea;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
    animation: newNotificationPulse 2s infinite;
}

.notification-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    margin-left: 15px;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-title-text {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.notification-message {
    font-size: 13px;
    color: #7f8c8d;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.notification-time {
    font-size: 11px;
    color: #bdc3c7;
    display: flex;
    align-items: center;
}

.notification-time i {
    margin-left: 5px;
}

/* حالة فارغة */
.notification-empty {
    text-align: center;
    padding: 40px 20px;
    color: #95a5a6;
}

.notification-empty i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.notification-empty h5 {
    margin-bottom: 10px;
    color: #7f8c8d;
}

.notification-empty p {
    margin: 0;
    font-size: 13px;
}

/* تذييل القائمة */
.notification-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.notification-footer .btn {
    margin: 0 5px;
    border-radius: 20px;
    padding: 8px 20px;
    font-size: 12px;
    transition: all 0.3s ease;
}

.notification-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* الرسوم المتحركة */
@keyframes bellRing {
    0%, 100% { transform: rotate(0deg); }
    10%, 30%, 50%, 70%, 90% { transform: rotate(-10deg); }
    20%, 40%, 60%, 80% { transform: rotate(10deg); }
}

@keyframes pulse {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes countPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes newNotificationPulse {
    0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
    50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
}

/* إشعارات داخل التطبيق */
.notification-toast {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 9999;
    min-width: 350px;
    max-width: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-left: 4px solid #667eea;
    animation: slideInLeft 0.5s ease-out;
    overflow: hidden;
}

.notification-toast.success {
    border-left-color: #2ecc71;
}

.notification-toast.warning {
    border-left-color: #f39c12;
}

.notification-toast.danger {
    border-left-color: #e74c3c;
}

.notification-toast.info {
    border-left-color: #3498db;
}

.notification-toast-content {
    padding: 20px;
    display: flex;
    align-items: flex-start;
}

.notification-toast-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    margin-left: 15px;
    flex-shrink: 0;
}

.notification-toast-text {
    flex: 1;
}

.notification-toast-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.notification-toast-message {
    font-size: 14px;
    color: #7f8c8d;
    margin: 0;
    line-height: 1.4;
}

.notification-toast-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 18px;
    color: #bdc3c7;
    cursor: pointer;
    transition: color 0.3s ease;
}

.notification-toast-close:hover {
    color: #7f8c8d;
}

.notification-toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    animation: progressBar 5s linear;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes progressBar {
    from { width: 100%; }
    to { width: 0%; }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .notification-dropdown {
        width: 320px !important;
        max-height: 400px !important;
    }

    .notification-toast {
        min-width: 300px;
        max-width: 320px;
        top: 10px;
        left: 10px;
        right: 10px;
    }

    .notification-header {
        padding: 15px;
    }

    .notification-item {
        padding: 12px 15px;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .notification-dropdown {
        background: #2c3e50;
        color: white;
    }

    .notification-item {
        border-bottom-color: #34495e;
    }

    .notification-item:hover {
        background: #34495e;
    }

    .notification-footer {
        background: #34495e;
        border-top-color: #2c3e50;
    }
}

/* تأثيرات إضافية للتفاعل */
.notification-bell-icon {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.notification-count-badge {
    font-family: 'Arial', sans-serif;
    letter-spacing: -0.5px;
}

.notification-item:active {
    transform: translateX(-5px);
    background: #e8f0fe;
}

/* تحسين الأداء */
.notification-list-container {
    will-change: scroll-position;
    -webkit-overflow-scrolling: touch;
}

.notification-item {
    will-change: transform, background-color;
}

/* شريط التمرير المخصص */
.notification-list-container::-webkit-scrollbar {
    width: 6px;
}

.notification-list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notification-list-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notification-list-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* أزرار الإشعارات في الجرس */
.notification-item .notification-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
    margin-right: 10px;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.notification-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
    margin-left: 3px;
    border-radius: 4px;
}

.notification-item {
    cursor: pointer;
    position: relative;
}

.notification-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
}
