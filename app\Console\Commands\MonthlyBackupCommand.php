<?php

namespace App\Console\Commands;

use App\Models\BackupSetting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class MonthlyBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'backup:monthly';

    /**
     * The console command description.
     */
    protected $description = 'إنشاء نسخة احتياطية شهرية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $settings = BackupSetting::getSettings();

        if (!$settings->auto_backup_enabled || !$settings->monthly_backup) {
            $this->info('النسخ الاحتياطي الشهري معطل');
            return 0;
        }

        $this->info('بدء النسخ الاحتياطي الشهري...');

        return Artisan::call('backup:create', [
            '--type' => 'monthly'
        ]);
    }
}
