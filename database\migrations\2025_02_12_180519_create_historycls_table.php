<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('historycls', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('clients_id');
            $table->unsignedBigInteger('clientschallenges_id')->nullable();;
            $table->unsignedBigInteger('clientssubscriptions_id')->nullable();;
            $table->unsignedBigInteger('subscriptions_id')->nullable();;
            $table->unsignedBigInteger('visit_id')->nullable();;
            $table->unsignedBigInteger('challenges_id')->nullable();;
            $table->unsignedBigInteger('challengesandvisitsheads_id')->nullable();;
            $table->string('type_history', 25);
            $table->foreign('clients_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('clientschallenges_id')->references('id')->on('clientschallenges');
            $table->foreign('clientssubscriptions_id')->references('id')->on('clientssubscriptions');
            $table->foreign('subscriptions_id')->references('id')->on('subscriptions');
            $table->foreign('visit_id')->references('id')->on('visits');
            $table->foreign('challenges_id')->references('id')->on('challenges');
            $table->foreign('challengesandvisitsheads_id')->references('id')->on('challengesandvisitsheads');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('historycls');
    }
};
