<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('infocompanies', function (Blueprint $table) {
            $table->id();
            $table->string('nameofcompany')->nullable();
            $table->string('nameofcompanymanger')->nullable();
            $table->string('nameofcompanyurlmon')->nullable();
            $table->string('titleofcompany')->nullable();
            $table->string('detailsofcompany')->nullable();
            $table->date('dateofinsertofcompany')->nullable();
            $table->enum('langueofcompany',allowed: ['arabic','english'])->default('arabic');
            $table->string('imagepathofcompany')->nullable();
            $table->string('slugimagepathofcompany')->nullable();
            $table->string('emailofcompany')->nullable();
            $table->string('phoneofcompany')->nullable();
            $table->string('phone2ofcompany')->nullable();
            $table->string('nameofprogrammarmon')->nullable();
            $table->string('phonenameofprogrammarmon')->nullable();
            $table->string('urlmon')->nullable();
            $table->date('dateofinsertofactive')->nullable();
            $table->date('dateofendofactive')->nullable();

            $table->timestamps();



        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('infocompanies');
    }
};
