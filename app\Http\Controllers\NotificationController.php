<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\NotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * عرض جميع الإشعارات للمستخدم الحالي
     */
    public function index()
    {
        $notifications = Notification::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('notifications.index', compact('notifications'));
    }

    /**
     * الحصول على جميع الإشعارات غير المقروءة من جدول notifications
     */
    public function getUnread()
    {
        $notifications = Notification::where('user_id', Auth::id())
                                   ->where('is_read', false)
                                   ->orderBy('created_at', 'desc')
                                   ->limit(20)
                                   ->get();

        return response()->json([
            'count' => $notifications->count(),
            'notifications' => $notifications
        ]);
    }

    /**
     * تحديد إشعار كمقروء
     */
    public function markAsRead($id)
    {
        $notification = Notification::where('id', $id)
            ->where('user_id', Auth::id())
            ->first();

        if ($notification) {
            $notification->markAsRead();
            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false], 404);
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead()
    {
        Notification::where('user_id', Auth::id())
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return response()->json(['success' => true]);
    }

    /**
     * حذف إشعار
     */
    public function delete($id)
    {
        $notification = Notification::where('id', $id)
            ->where('user_id', Auth::id())
            ->first();

        if ($notification) {
            $notification->delete();
            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false], 404);
    }

    /**
     * حذف جميع الإشعارات المقروءة
     */
    public function deleteRead()
    {
        Notification::where('user_id', Auth::id())
            ->where('is_read', true)
            ->delete();

        return response()->json(['success' => true]);
    }

    /**
     * عرض صفحة إعدادات الإشعارات
     */
    public function settings()
    {
        $settings = NotificationSetting::getForUser(Auth::id());
        $availableSounds = NotificationSetting::getAvailableSounds();

        return view('notifications.settings', compact('settings', 'availableSounds'));
    }

    /**
     * تحديث إعدادات الإشعارات
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'sound_enabled' => 'boolean',
            'sound_file' => 'string',
            'sound_volume' => 'integer|min:0|max:100',
            'desktop_notifications' => 'boolean',
            'new_client_notifications' => 'boolean',
            'new_visit_notifications' => 'boolean',
            'payment_notifications' => 'boolean',
            'challenge_notifications' => 'boolean'
        ]);

        NotificationSetting::updateForUser(Auth::id(), $request->all());

        session()->flash('success', 'تم تحديث إعدادات الإشعارات بنجاح');
        return redirect()->back();
    }

    /**
     * الحصول على إعدادات الإشعارات (AJAX)
     */
    public function getSettings()
    {
        $settings = NotificationSetting::getForUser(Auth::id());

        return response()->json([
            'sound_enabled' => $settings->sound_enabled,
            'sound_file' => $settings->sound_file,
            'sound_volume' => $settings->sound_volume,
            'desktop_notifications' => $settings->desktop_notifications,
            'new_client_notifications' => $settings->new_client_notifications,
            'new_visit_notifications' => $settings->new_visit_notifications,
            'payment_notifications' => $settings->payment_notifications,
            'challenge_notifications' => $settings->challenge_notifications
        ]);
    }

    /**
     * اختبار الصوت
     */
    public function testSound(Request $request)
    {
        $soundFile = $request->input('sound_file', 'notification.mp3');
        $volume = $request->input('volume', 50);

        return response()->json([
            'success' => true,
            'sound_file' => $soundFile,
            'volume' => $volume
        ]);
    }

    /**
     * إنشاء إشعار تجريبي
     */
    public function createTestNotification()
    {
        // إنشاء إشعار عميل جديد تجريبي ليظهر في الجرس
        $notification = Notification::createNotification([
            'type' => 'new_client',
            'title' => '👤 عميل تجريبي',
            'message' => 'تم إنشاء عميل تجريبي لاختبار النظام - ' . now()->format('H:i:s'),
            'icon' => 'fa-user-plus',
            'color' => 'success',
            'user_id' => Auth::id(),
            'data' => [
                'test' => true,
                'client_name' => 'عميل تجريبي',
                'client_phone' => '0500000000',
                'created_by' => Auth::user()->name,
                'created_at' => now()->format('Y-m-d H:i:s')
            ]
        ]);

        return response()->json([
            'success' => true,
            'notification_id' => $notification->id,
            'message' => 'تم إنشاء إشعار تجريبي بنجاح'
        ]);
    }
}
