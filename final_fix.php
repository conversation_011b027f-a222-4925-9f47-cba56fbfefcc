<?php

/**
 * إصلاح نهائي لجميع مشاكل نظام الإشعارات
 * تشغيل: php final_fix.php
 */

echo "🔧 إصلاح نهائي لنظام الإشعارات والنسخ الاحتياطي\n";
echo "=" . str_repeat("=", 55) . "\n\n";

// 1. مسح جميع أنواع الـ cache
echo "1️⃣ مسح cache...\n";
$cacheCommands = [
    'php artisan cache:clear',
    'php artisan config:clear', 
    'php artisan route:clear',
    'php artisan view:clear',
    'php artisan optimize:clear'
];

foreach ($cacheCommands as $command) {
    echo "تشغيل: {$command}\n";
    exec($command, $output, $return);
    if ($return === 0) {
        echo "✅ نجح\n";
    } else {
        echo "⚠️  تحذير\n";
    }
}
echo "\n";

// 2. تحديث autoloader
echo "2️⃣ تحديث autoloader...\n";
exec('composer dump-autoload', $output, $return);
if ($return === 0) {
    echo "✅ تم تحديث autoloader بنجاح\n";
} else {
    echo "❌ فشل في تحديث autoloader\n";
}
echo "\n";

// 3. إنشاء المجلدات المطلوبة
echo "3️⃣ إنشاء المجلدات...\n";
$directories = [
    'storage/backups',
    'storage/app/backups',
    'storage/app/temp', 
    'public/assets/sounds',
    'public/assets/css',
    'public/assets/js'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "📁 تم إنشاء: {$dir}\n";
    } else {
        echo "✅ موجود: {$dir}\n";
    }
    
    // تعيين الصلاحيات
    chmod($dir, 0755);
}
echo "\n";

// 4. فحص وإصلاح ملف CSRF
echo "4️⃣ فحص CSRF token...\n";
$headFile = 'resources/views/layouts/head.blade.php';
if (file_exists($headFile)) {
    $content = file_get_contents($headFile);
    if (strpos($content, 'csrf-token') === false) {
        // إضافة CSRF token
        $newContent = str_replace(
            '<title> @yield("title") </title>',
            '<title> @yield("title") </title>' . "\n" . '<meta name="csrf-token" content="{{ csrf_token() }}">',
            $content
        );
        file_put_contents($headFile, $newContent);
        echo "✅ تم إضافة CSRF token\n";
    } else {
        echo "✅ CSRF token موجود\n";
    }
} else {
    echo "❌ ملف head.blade.php غير موجود\n";
}
echo "\n";

// 5. فحص وإصلاح الهيدر
echo "5️⃣ فحص الهيدر...\n";
$headerFile = 'resources/views/layouts/main-header.blade.php';
if (file_exists($headerFile)) {
    $content = file_get_contents($headerFile);
    
    // إزالة التعليقات من زر الجرس
    if (strpos($content, '<!--') !== false && strpos($content, 'notification-bell') !== false) {
        $content = str_replace('<!--', '', $content);
        $content = str_replace('-->', '', $content);
        file_put_contents($headerFile, $content);
        echo "✅ تم إزالة التعليق من زر الجرس\n";
    } else {
        echo "✅ زر الجرس مُفعل\n";
    }
} else {
    echo "❌ ملف main-header.blade.php غير موجود\n";
}
echo "\n";

// 6. فحص JavaScript
echo "6️⃣ فحص JavaScript...\n";
$jsFile = 'public/assets/js/notifications.js';
if (file_exists($jsFile)) {
    $jsContent = file_get_contents($jsFile);
    
    // إصلاح مشكلة getAttribute
    if (strpos($jsContent, 'getAttribute(\'content\')') !== false) {
        $fixedContent = str_replace(
            'document.querySelector(\'meta[name="csrf-token"]\')?.getAttribute(\'content\')',
            'document.querySelector(\'meta[name="csrf-token"]\') ? document.querySelector(\'meta[name="csrf-token"]\').getAttribute(\'content\') : \'\'',
            $jsContent
        );
        file_put_contents($jsFile, $fixedContent);
        echo "✅ تم إصلاح مشكلة getAttribute\n";
    } else {
        echo "✅ JavaScript سليم\n";
    }
} else {
    echo "❌ ملف notifications.js غير موجود\n";
}
echo "\n";

// 7. تشغيل الهجرات
echo "7️⃣ تشغيل الهجرات...\n";
exec('php artisan migrate --force', $output, $return);
if ($return === 0) {
    echo "✅ تم تشغيل الهجرات بنجاح\n";
} else {
    echo "⚠️  تحذير في الهجرات (قد تكون موجودة مسبقاً)\n";
}
echo "\n";

// 8. إنشاء إعدادات افتراضية
echo "8️⃣ إنشاء الإعدادات الافتراضية...\n";
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    // إنشاء إعدادات النسخ الاحتياطي
    $backupSettings = App\Models\BackupSetting::getSettings();
    echo "✅ إعدادات النسخ الاحتياطي جاهزة\n";
    
    // إنشاء إشعار ترحيبي
    $user = App\Models\User::first();
    if ($user) {
        App\Models\Notification::createNotification([
            'type' => 'system_ready',
            'title' => '🎉 النظام جاهز!',
            'message' => 'تم إعداد نظام الإشعارات والنسخ الاحتياطي بنجاح',
            'icon' => 'fa-check-circle',
            'color' => 'success',
            'user_id' => $user->id
        ]);
        echo "✅ تم إنشاء إشعار ترحيبي\n";
    }
    
} catch (Exception $e) {
    echo "⚠️  تحذير: " . $e->getMessage() . "\n";
}
echo "\n";

// 9. إنشاء ملفات أصوات تجريبية
echo "9️⃣ إنشاء ملفات أصوات تجريبية...\n";
$soundsDir = 'public/assets/sounds/';
$sounds = ['notification.mp3', 'bell.mp3', 'chime.mp3', 'ding.mp3', 'pop.mp3', 'swoosh.mp3'];

foreach ($sounds as $sound) {
    $soundPath = $soundsDir . $sound;
    if (!file_exists($soundPath)) {
        // إنشاء ملف نصي كمرجع
        file_put_contents($soundPath, "Placeholder for {$sound} - Replace with actual MP3 file");
        echo "📄 تم إنشاء مرجع: {$sound}\n";
    } else {
        echo "✅ موجود: {$sound}\n";
    }
}
echo "\n";

// 10. اختبار النظام
echo "🔟 اختبار النظام...\n";
try {
    // اختبار المسارات
    $routes = ['notifications.index', 'notifications.settings', 'backup.index'];
    foreach ($routes as $route) {
        try {
            $url = route($route);
            echo "✅ مسار {$route}: {$url}\n";
        } catch (Exception $e) {
            echo "❌ مسار {$route}: خطأ\n";
        }
    }
    
    // اختبار قاعدة البيانات
    $tables = ['notifications', 'notification_settings', 'backup_settings', 'backup_logs'];
    foreach ($tables as $table) {
        try {
            $count = DB::table($table)->count();
            echo "✅ جدول {$table}: {$count} سجل\n";
        } catch (Exception $e) {
            echo "❌ جدول {$table}: غير موجود\n";
        }
    }
    
} catch (Exception $e) {
    echo "⚠️  تحذير في الاختبار: " . $e->getMessage() . "\n";
}
echo "\n";

// 11. تقرير نهائي
echo "📊 التقرير النهائي:\n";
echo "=" . str_repeat("=", 25) . "\n";

$checkList = [
    'CSRF Token' => file_exists('resources/views/layouts/head.blade.php') && 
                   strpos(file_get_contents('resources/views/layouts/head.blade.php'), 'csrf-token') !== false,
    'JavaScript' => file_exists('public/assets/js/notifications.js'),
    'CSS' => file_exists('public/assets/css/notifications.css'),
    'Controllers' => file_exists('app/Http/Controllers/NotificationController.php') && 
                    file_exists('app/Http/Controllers/BackupController.php'),
    'Models' => file_exists('app/Models/Notification.php'),
    'Views' => file_exists('resources/views/notifications/index.blade.php'),
    'Observer' => file_exists('app/Observers/ClientObserver.php'),
    'Sounds Directory' => is_dir('public/assets/sounds'),
    'Backup Directory' => is_dir('storage/backups')
];

foreach ($checkList as $item => $status) {
    echo ($status ? "✅" : "❌") . " {$item}\n";
}

echo "\n🎯 الخطوات التالية:\n";
echo "1. زيارة /notifications لاختبار الإشعارات\n";
echo "2. زيارة /notifications/settings للإعدادات\n";
echo "3. زيارة /backup للنسخ الاحتياطي\n";
echo "4. إضافة ملفات MP3 حقيقية في public/assets/sounds/\n";
echo "5. اختبار إضافة عميل جديد\n";
echo "6. اختبار زر الإشعارات في الهيدر\n\n";

echo "🎉 تم الانتهاء من الإصلاح النهائي!\n";
echo "💡 إذا استمرت المشاكل، تحقق من:\n";
echo "   - storage/logs/laravel.log\n";
echo "   - console المتصفح\n";
echo "   - إعدادات قاعدة البيانات\n";

?>
