@extends('layouts.master')

@section('title')
إعدادات الإشعارات
@stop

@section('css')
<style>
.settings-card {
    border: none;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.volume-slider {
    width: 100%;
    height: 5px;
    border-radius: 5px;
    background: #ddd;
    outline: none;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.volume-slider:hover {
    opacity: 1;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
}

.volume-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
}

.sound-test-btn {
    transition: all 0.3s ease;
}

.sound-test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}
</style>
@endsection

@section('page-header')
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">الإعدادات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ إعدادات الإشعارات</span>
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8 col-md-12">
        <div class="card settings-card">
            <div class="card-header settings-header">
                <h3 class="card-title mb-0">
                    <i class="fa fa-bell me-2"></i>
                    إعدادات الإشعارات
                </h3>
            </div>

            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fa fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <form action="{{ route('notifications.settings.update') }}" method="POST" id="settingsForm">
                    @csrf

                    <!-- إعدادات الصوت -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fa fa-volume-up me-2"></i>
                                إعدادات الصوت
                            </h5>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label d-flex justify-content-between align-items-center">
                                    تفعيل الصوت
                                    <label class="switch">
                                        <input type="checkbox" name="sound_enabled" value="1"
                                               {{ $settings->sound_enabled ? 'checked' : '' }} id="soundEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">اختيار الصوت</label>
                                <select name="sound_file" class="form-control" id="soundFile">
                                    @foreach($availableSounds as $file => $name)
                                        <option value="{{ $file }}" {{ $settings->sound_file == $file ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label">مستوى الصوت: <span id="volumeValue">{{ $settings->sound_volume }}%</span></label>
                                <input type="range" name="sound_volume" min="0" max="100"
                                       value="{{ $settings->sound_volume }}" class="volume-slider" id="volumeSlider">
                            </div>
                        </div>

                        <div class="col-12">
                            <button type="button" class="btn btn-info sound-test-btn" id="testSoundBtn">
                                <i class="fa fa-play me-2"></i>
                                اختبار الصوت
                            </button>
                        </div>
                    </div>

                    <hr>

                    <!-- إعدادات الإشعارات -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fa fa-desktop me-2"></i>
                                إعدادات العرض
                            </h5>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label d-flex justify-content-between align-items-center">
                                    إشعارات سطح المكتب
                                    <label class="switch">
                                        <input type="checkbox" name="desktop_notifications" value="1"
                                               {{ $settings->desktop_notifications ? 'checked' : '' }} id="desktopNotifications">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                                <small class="text-muted">عرض إشعارات على سطح المكتب</small>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-info" id="requestPermissionBtn">
                                        <i class="fa fa-shield-alt me-1"></i>
                                        طلب الإذن
                                    </button>
                                    <span class="badge badge-success ms-2" id="permissionStatus" style="display: none;">
                                        <i class="fa fa-check"></i> مُفعل
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- أنواع الإشعارات -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="fa fa-list me-2"></i>
                                أنواع الإشعارات
                            </h5>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label d-flex justify-content-between align-items-center">
                                    إشعارات العملاء الجدد
                                    <label class="switch">
                                        <input type="checkbox" name="new_client_notifications" value="1"
                                               {{ $settings->new_client_notifications ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label d-flex justify-content-between align-items-center">
                                    إشعارات الزيارات الجديدة
                                    <label class="switch">
                                        <input type="checkbox" name="new_visit_notifications" value="1"
                                               {{ $settings->new_visit_notifications ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label d-flex justify-content-between align-items-center">
                                    إشعارات المدفوعات
                                    <label class="switch">
                                        <input type="checkbox" name="payment_notifications" value="1"
                                               {{ $settings->payment_notifications ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label d-flex justify-content-between align-items-center">
                                    إشعارات التحديات
                                    <label class="switch">
                                        <input type="checkbox" name="challenge_notifications" value="1"
                                               {{ $settings->challenge_notifications ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- أزرار الحفظ والاختبار -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save me-2"></i>
                                        حفظ الإعدادات
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                        <i class="fa fa-arrow-left me-2"></i>
                                        رجوع
                                    </button>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-warning" id="testNotificationBtn">
                                        <i class="fa fa-flask me-2"></i>
                                        إشعار تجريبي
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="col-lg-4 col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fa fa-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fa fa-lightbulb me-2"></i>نصائح:</h6>
                    <ul class="mb-0">
                        <li>تأكد من تفعيل إشعارات المتصفح للحصول على إشعارات سطح المكتب</li>
                        <li>يمكنك اختبار الصوت قبل الحفظ</li>
                        <li>الإشعارات التجريبية تساعد في التأكد من عمل النظام</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fa fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                    <p class="mb-0">قد تحتاج إلى إعادة تحميل الصفحة بعد تغيير الإعدادات لتطبيق التغييرات بشكل كامل.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث قيمة مستوى الصوت
    const volumeSlider = document.getElementById('volumeSlider');
    const volumeValue = document.getElementById('volumeValue');

    volumeSlider.addEventListener('input', function() {
        volumeValue.textContent = this.value + '%';
    });

    // اختبار الصوت
    document.getElementById('testSoundBtn').addEventListener('click', function() {
        const soundFile = document.getElementById('soundFile').value;
        const volume = document.getElementById('volumeSlider').value;
        const soundEnabled = document.getElementById('soundEnabled').checked;

        if (!soundEnabled) {
            alert('يجب تفعيل الصوت أولاً');
            return;
        }

        if (window.notificationSystem) {
            window.notificationSystem.testSound(soundFile, volume);
        }
    });

    // إنشاء إشعار تجريبي
    document.getElementById('testNotificationBtn').addEventListener('click', function() {
        if (window.notificationSystem) {
            window.notificationSystem.createTestNotification();

            // عرض رسالة تأكيد
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fa fa-check me-2"></i>
                تم إنشاء إشعار تجريبي! تحقق من الإشعارات.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const form = document.getElementById('settingsForm');
            form.insertBefore(alert, form.firstChild);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
    });

    // طلب إذن الإشعارات
    document.getElementById('requestPermissionBtn')?.addEventListener('click', async function() {
        const btn = this;
        const originalText = btn.innerHTML;

        btn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>جاري الطلب...';
        btn.disabled = true;

        try {
            if ('Notification' in window) {
                const permission = await Notification.requestPermission();

                if (permission === 'granted') {
                    document.getElementById('permissionStatus').style.display = 'inline-block';
                    btn.innerHTML = '<i class="fa fa-check me-1"></i>تم المنح';
                    btn.className = 'btn btn-sm btn-success';

                    // إظهار إشعار ترحيبي
                    if (window.notificationSystem) {
                        window.notificationSystem.showWelcomeNotification();
                    }
                } else {
                    btn.innerHTML = '<i class="fa fa-times me-1"></i>تم الرفض';
                    btn.className = 'btn btn-sm btn-danger';
                }
            } else {
                btn.innerHTML = '<i class="fa fa-exclamation me-1"></i>غير مدعوم';
                btn.className = 'btn btn-sm btn-warning';
            }
        } catch (error) {
            console.error('خطأ في طلب الإذن:', error);
            btn.innerHTML = '<i class="fa fa-exclamation-triangle me-1"></i>خطأ';
            btn.className = 'btn btn-sm btn-danger';
        }

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            btn.className = 'btn btn-sm btn-info';
        }, 3000);
    });

    // فحص حالة الإذن عند تحميل الصفحة
    if ('Notification' in window && Notification.permission === 'granted') {
        document.getElementById('permissionStatus').style.display = 'inline-block';
        const btn = document.getElementById('requestPermissionBtn');
        if (btn) {
            btn.innerHTML = '<i class="fa fa-check me-1"></i>مُفعل';
            btn.className = 'btn btn-sm btn-success';
        }
    }

    // حفظ الإعدادات وتحديث النظام
    document.getElementById('settingsForm').addEventListener('submit', function(e) {
        // السماح بإرسال النموذج بشكل طبيعي
        // ولكن تحديث إعدادات النظام بعد الحفظ
        setTimeout(() => {
            if (window.notificationSystem) {
                const formData = new FormData(this);
                const settings = {};

                for (let [key, value] of formData.entries()) {
                    if (key.includes('_notifications') || key === 'sound_enabled' || key === 'desktop_notifications') {
                        settings[key] = true;
                    } else {
                        settings[key] = value;
                    }
                }

                window.notificationSystem.updateSettings(settings);
            }
        }, 100);
    });
});
</script>
@endsection
