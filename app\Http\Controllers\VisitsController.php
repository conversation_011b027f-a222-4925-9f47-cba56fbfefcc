<?php

namespace App\Http\Controllers;

use App\Models\visits;
use App\Models\historycls;
use Illuminate\Http\Request;
use App\Models\challengesandvisitsheads;





class VisitsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $visits = visits::all()->sortByDesc("id");
        return view('visits.visits',compact('visits'));

        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $validatedData = $request->validate([
            'note' => 'required:visits',
        ],[

            'note.required' =>'يرجي ادخال الملاحظة',



        ]);

        if ($request->challenges_id =='') {


 $visitsss=visits::create([
    'clients_id' => $request->clients_id,
   'dateofvisit' => $request->dateofvisit,
   'nextvisit' => $request->nextvisit,
   'note' => $request->note,
]);

historycls::create([
    'visit_id' => $visitsss->id,
    'clients_id' => $request->clients_id,
    'type_history' => "zeyara",

]);



}

else
{

    $visitsss=visits::create([
        'clients_id' => $request->clients_id,
       'dateofvisit' => $request->dateofvisit,
       'nextvisit' => $request->nextvisit,
       'note' => $request->note,
    ]);

    challengesandvisitsheads::create([
        'visit_id' => $request->visit_id,
       'challenge_id' => $request->challenges_id,

    ]);

    historycls::create([
        'visit_id' => $visitsss->id,
        'clients_id' => $request->clients_id,
         'challenges_id'=> $request->challenges_id,
        'type_history' => "zeyara",

    ]);

}



        session()->flash('Add', 'تم اضافة الزيارة بنجاح ');
        return redirect()->back();


    }



    /**
     * Display the specified resource.
     */
    public function show(visits $visits)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(visits $visits)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $id = $request->id;


        $validate = $request->validate([




            'dateofvisit' => 'required:visits,note,'.$id,
        ],[

            'dateofvisit.required' =>'يرجي ادخال تاريخ الزيارة',

        ]);

        $visits = visits::find($id);
        $visits->update([
            'dateofvisit' => $request->dateofvisit,
            'nextvisit' => $request->nextvisit,
            'note' => $request->note,



        ]);

        session()->flash('edit','تم تعديل الموعد بنجاج');
        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {


        $id = $request->id;
        visits::find($id)->delete();
        session()->flash('delete','تم حذف الموعد بنجاح');
        return redirect()->back();
        //

    //
}
}
