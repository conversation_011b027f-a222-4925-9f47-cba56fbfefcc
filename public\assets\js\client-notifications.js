/**
 * إشعارات العملاء الجدد
 * يتم تضمين هذا الملف في صفحة إضافة العملاء
 */

// دالة لإرسال إشعار فوري عند إضافة عميل
function notifyNewClientAdded(clientData) {
    // إشعار سريع في الصفحة
    showQuickAlert('success', `تم إضافة العميل: ${clientData.name} بنجاح!`);
    
    // إشعار للنظام العام
    if (window.notificationSystem) {
        window.notificationSystem.notifyNewClient(clientData);
    }
    
    // إشعار صوتي
    playSuccessSound();
}

// دالة عرض إشعار سريع
function showQuickAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-left: 4px solid ${type === 'success' ? '#28a745' : '#dc3545'};
    `;
    
    alertDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fa fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} text-${type} me-2" style="font-size: 1.3em;"></i>
            <div>
                <strong>${type === 'success' ? 'نجح!' : 'خطأ!'}</strong><br>
                <small>${message}</small>
            </div>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// دالة تشغيل صوت النجاح
function playSuccessSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        // نغمة نجاح مميزة
        oscillator.frequency.value = 800;
        oscillator.type = "sine";
        
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
        
        // نغمة ثانية أعلى
        setTimeout(() => {
            const oscillator2 = audioContext.createOscillator();
            const gainNode2 = audioContext.createGain();
            
            oscillator2.connect(gainNode2);
            gainNode2.connect(audioContext.destination);
            
            oscillator2.frequency.value = 1000;
            oscillator2.type = "sine";
            
            gainNode2.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode2.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.01);
            gainNode2.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator2.start(audioContext.currentTime);
            oscillator2.stop(audioContext.currentTime + 0.3);
        }, 200);
        
    } catch (error) {
        console.warn('فشل في تشغيل صوت النجاح:', error);
    }
}

// مراقبة نماذج إضافة العملاء
document.addEventListener('DOMContentLoaded', function() {
    // البحث عن نموذج إضافة عميل
    const clientForm = document.querySelector('form[action*="clients"]');
    
    if (clientForm) {
        clientForm.addEventListener('submit', function(e) {
            // الحصول على بيانات العميل من النموذج
            const formData = new FormData(this);
            const clientData = {
                name: formData.get('name') || 'عميل جديد',
                phone: formData.get('phone') || '',
                gender: formData.get('gender') || '',
                subscription_date: formData.get('subscroptiondate') || ''
            };
            
            // إرسال الإشعار بعد ثانية واحدة (للسماح بحفظ البيانات)
            setTimeout(() => {
                notifyNewClientAdded(clientData);
            }, 1000);
        });
    }
    
    // مراقبة رسائل النجاح من Laravel
    const successMessage = document.querySelector('.alert-success');
    if (successMessage && successMessage.textContent.includes('تم إضافة العميل')) {
        // استخراج اسم العميل من الرسالة
        const clientName = extractClientNameFromMessage(successMessage.textContent);
        
        notifyNewClientAdded({
            name: clientName,
            phone: '',
            gender: '',
            subscription_date: ''
        });
    }
});

// دالة استخراج اسم العميل من رسالة النجاح
function extractClientNameFromMessage(message) {
    const match = message.match(/تم إضافة العميل\s*:?\s*([^،\s]+)/);
    return match ? match[1] : 'عميل جديد';
}

// تصدير الدوال للاستخدام العام
window.notifyNewClientAdded = notifyNewClientAdded;
window.showQuickAlert = showQuickAlert;
