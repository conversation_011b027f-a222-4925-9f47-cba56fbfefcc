# 🔔 نظام الإشعارات والنسخ الاحتياطي المتقدم

نظام متكامل للإشعارات الفورية والنسخ الاحتياطي التلقائي مصمم خصيصاً لنظام إدارة التغذية والعملاء.

## ✨ الميزات الرئيسية

### 🔔 نظام الإشعارات المتقدم

#### الإشعارات التلقائية:
- ✅ إشعار فوري عند إضافة عميل جديد
- ✅ إشعار عند تحديث بيانات العميل
- ✅ إشعار عند حذف العميل
- ✅ إشعارات حالة النسخ الاحتياطي

#### الميزات التفاعلية:
- 🔊 **أصوات قابلة للتخصيص** - 6 أصوات مختلفة
- 🎚️ **تحكم في مستوى الصوت** - من 0 إلى 100%
- 🖥️ **إشعارات سطح المكتب** - إشعارات Windows جميلة
- 📱 **إشعارات داخل التطبيق** - تنبيهات فورية أنيقة
- 🔢 **عداد الإشعارات** - رقم بجانب أيقونة الجرس
- 📋 **قائمة الإشعارات** - عرض منظم وتفاعلي

#### إدارة الإشعارات:
- 👁️ تحديد كمقروء/غير مقروء
- 🗑️ حذف الإشعارات
- 🔍 تصفية حسب النوع والتاريخ
- ⚙️ إعدادات شخصية لكل مستخدم
- 📊 إحصائيات مفصلة

### 💾 نظام النسخ الاحتياطي الذكي

#### النسخ التلقائي:
- 📅 **نسخ يومي** - كل يوم في الساعة 2:00 صباحاً
- 📅 **نسخ أسبوعي** - كل جمعة في الساعة 3:00 صباحاً  
- 📅 **نسخ شهري** - أول يوم من كل شهر في الساعة 4:00 صباحاً

#### الميزات المتقدمة:
- 🗜️ **ضغط النسخ** - توفير مساحة التخزين
- 📋 **اختيار الجداول** - نسخ جداول محددة فقط
- 🔗 **النسخ الخارجي** - رفع إلى خوادم خارجية
- 🧹 **حذف تلقائي** - حذف النسخ القديمة
- 📊 **سجل مفصل** - تتبع جميع عمليات النسخ
- ⬇️ **تحميل النسخ** - تحميل النسخ الاحتياطية

## 🎯 المستخدمون المستهدفون

### حسب الدور:
- **المدير العام (Super)**: جميع الميزات + إدارة النسخ الاحتياطي
- **المدير (Admin)**: الإشعارات + عرض النسخ الاحتياطي
- **المستخدم العادي**: الإشعارات الأساسية
- **السكرتير**: إشعارات العملاء والزيارات

## 🚀 التثبيت السريع

### 1. تشغيل الهجرات:
```bash
php artisan migrate
```

### 2. إنشاء الإعدادات الافتراضية:
```bash
php artisan tinker
App\Models\BackupSetting::getSettings();
```

### 3. إعداد جدولة المهام:
```bash
# إضافة إلى crontab
* * * * * cd /path-to-project && php artisan schedule:run >> /dev/null 2>&1
```

### 4. إضافة ملفات الأصوات:
- ضع ملفات MP3 في `public/assets/sounds/`
- الأصوات المطلوبة: notification.mp3, bell.mp3, chime.mp3

## 📱 واجهة المستخدم

### 🔔 أيقونة الإشعارات
```html
<!-- في الهيدر -->
<div class="dropdown nav-item main-header-notification">
    <a class="new nav-link" href="#" id="notificationDropdown">
        <svg><!-- أيقونة الجرس --></svg>
        <span class="badge badge-danger notification-count">5</span>
    </a>
    <!-- قائمة الإشعارات -->
</div>
```

### ⚙️ صفحة الإعدادات
- تحكم في الأصوات ومستوى الصوت
- تفعيل/تعطيل أنواع الإشعارات
- إعدادات إشعارات سطح المكتب
- اختبار الإشعارات والأصوات

### 💾 صفحة النسخ الاحتياطي
- إحصائيات النسخ الاحتياطي
- إعدادات النسخ التلقائي
- إنشاء نسخ يدوية
- سجل النسخ الاحتياطي

## 🔧 التخصيص

### إضافة أصوات جديدة:
```php
// في NotificationSetting Model
public static function getAvailableSounds()
{
    return [
        'notification.mp3' => 'صوت افتراضي',
        'bell.mp3' => 'جرس',
        'your_sound.mp3' => 'صوتك المخصص'
    ];
}
```

### إضافة أنواع إشعارات جديدة:
```php
// في Observer
Notification::createNotification([
    'type' => 'new_payment',
    'title' => 'دفعة جديدة',
    'message' => 'تم استلام دفعة من العميل',
    'icon' => 'fa-money-bill',
    'color' => 'success',
    'user_id' => $user->id
]);
```

## 📊 الإحصائيات والتقارير

### إحصائيات الإشعارات:
- إجمالي الإشعارات
- الإشعارات غير المقروءة
- إشعارات اليوم
- إشعارات حسب النوع

### إحصائيات النسخ الاحتياطي:
- إجمالي النسخ
- النسخ الناجحة/الفاشلة
- حجم النسخ الاحتياطي
- مدة النسخ

## 🔒 الأمان

### حماية الإشعارات:
- ✅ التحقق من صلاحيات المستخدم
- ✅ تشفير البيانات الحساسة
- ✅ تنظيف الإشعارات القديمة

### حماية النسخ الاحتياطي:
- ✅ صلاحيات الملفات
- ✅ تشفير النسخ
- ✅ حماية مسارات التحميل

## 🎨 التصميم والواجهة

### الألوان والأيقونات:
- 🔵 **أزرق**: إشعارات عامة
- 🟢 **أخضر**: إشعارات النجاح
- 🟡 **أصفر**: إشعارات التحذير
- 🔴 **أحمر**: إشعارات الخطر

### الرسوم المتحركة:
- انزلاق الإشعارات
- نبضات العداد
- شريط التقدم للنسخ الاحتياطي

## 📱 التوافق

### المتصفحات المدعومة:
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### الأجهزة:
- 💻 أجهزة سطح المكتب
- 📱 الأجهزة اللوحية
- 📱 الهواتف الذكية

## 🔄 التحديثات المستقبلية

### الإصدار القادم:
- 📧 إشعارات البريد الإلكتروني
- 📱 إشعارات SMS
- ☁️ نسخ احتياطي سحابي
- 📊 تقارير متقدمة
- 🌐 واجهة برمجة تطبيقات

## 🆘 الدعم الفني

### استكشاف الأخطاء:
1. تحقق من `storage/logs/laravel.log`
2. تحقق من console المتصفح
3. تحقق من إعدادات قاعدة البيانات
4. تحقق من صلاحيات الملفات

### الأوامر المفيدة:
```bash
# اختبار الإشعارات
php artisan tinker
App\Models\Notification::createNotification([...]);

# إنشاء نسخة احتياطية
php artisan backup:create

# حذف النسخ القديمة
php artisan tinker
App\Models\BackupLog::deleteOldBackups(30);
```

## 📄 الترخيص

هذا النظام مطور خصيصاً لنظام إدارة التغذية والعملاء ومحمي بحقوق الطبع والنشر.

---

**تم التطوير بواسطة:** فريق التطوير المتخصص  
**التاريخ:** يوليو 2025  
**الإصدار:** 1.0.0

🎉 **استمتع بتجربة إشعارات ونسخ احتياطي متقدمة!**
