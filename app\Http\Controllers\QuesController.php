<?php

namespace App\Http\Controllers;

use App\Models\user;
use App\Models\ques;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;


class QuesController extends Controller
{
    use AuthorizesRequests;
    /**
     * Display a listing of the resource.
     */
  
        public function index(user $user)
        {
            $this->authorize('IsSoperAndAdmin',$user);
        $ques = ques::all();
        return view('ques.ques',compact('ques'));

        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'question' => 'required|unique:ques|max:255',
        ],[

            'question.required' =>'يرجي ادخال اسم القسم',
            'question.unique' =>'اسم القسم مسجل مسبقا',


        ]);






        ques::create([
            'question' => $request->question,
            'Created_by' => Auth::user()->name,

        ]);
        session()->flash('Add', 'تم اضافة القسم بنجاح ');
        return redirect('/ques');






        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ques $ques)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ques $ques)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {

        $id = $request->id;


        $validate = $request->validate([

        'question' => 'required|max:255|unique:ques,question,'.$id,
        ],[

            'question.required' =>'يرجي ادخال اسم القسم',
            'question.unique' =>'اسم القسم مسجل مسبقا',

        ]);

        $ques = ques::find($id);
        $ques->update([
            'question' => $request->question,

        ]);

        session()->flash('edit','تم تعديل السؤال بنجاج');
        return redirect('/ques');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {

        $id = $request->id;
        ques::find($id)->delete();
        session()->flash('delete','تم حذف القسم بنجاح');
        return redirect('/ques');
        //
    }
}
