<?php

namespace App\Models;

use App\Models\historycls;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class challenges extends Model
{
    use HasFactory;
    protected $guarded = [];


##########

public function Hed_estpsmsmssclint()
{
    return $this->belongsTo('App\Models\clientschallenges','id');
}

#########################








public function historyclsy()
{
    return $this->hasMany(historycls::class,'challenges_id');
}


}


