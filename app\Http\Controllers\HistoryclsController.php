<?php

namespace App\Http\Controllers;

use App\Models\historycls;
use Illuminate\Http\Request;

class HistoryclsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(historycls $historycls)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(historycls $historycls)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, historycls $historycls)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(historycls $historycls)
    {
        //
    }
}
