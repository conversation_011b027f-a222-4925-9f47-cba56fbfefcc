<?php

namespace App\Observers;

use App\Models\clients;
use App\Models\Notification;
use App\Models\NotificationSetting;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class ClientObserver
{
    /**
     * Handle the clients "created" event.
     */
    public function created(clients $client): void
    {
        try {
            // تسجيل في اللوج للتأكد من تشغيل Observer
            \Log::info('ClientObserver: تم إنشاء عميل جديد', [
                'client_id' => $client->id,
                'client_name' => $client->name,
                'client_phone' => $client->phone
            ]);

            // إنشاء إشعار لجميع المستخدمين المخولين
            $users = User::whereIn('role', ['super', 'admin', 'secretary'])->get();

            \Log::info('ClientObserver: عدد المستخدمين المستهدفين', ['count' => $users->count()]);

            foreach ($users as $user) {
                try {
                    // إنشاء الإشعار مباشرة بدون فحص الإعدادات أولاً
                    $notification = Notification::createNotification([
                        'type' => 'new_client',
                        'title' => '👤 عميل جديد',
                        'message' => "تم إضافة العميل: {$client->name}" .
                                   ($client->phone ? " - هاتف: {$client->phone}" : "") .
                                   " - تاريخ الإضافة: " . now()->format('H:i') .
                                   " - بواسطة: " . (Auth::user()->name ?? 'النظام'),
                        'icon' => 'fa-user-plus',
                        'color' => 'success',
                        'user_id' => $user->id,
                        'related_id' => $client->id,
                        'related_type' => 'App\Models\clients',
                        'data' => [
                            'client_id' => $client->id,
                            'client_name' => $client->name,
                            'client_phone' => $client->phone,
                            'client_gender' => $client->gender,
                            'subscription_date' => $client->subscroptiondate,
                            'created_by' => Auth::user()->name ?? 'النظام',
                            'action_url' => '/clients',
                            'timestamp' => now()->toDateTimeString(),
                            'is_urgent' => true // إشعار عاجل
                        ]
                    ]);

                    \Log::info('ClientObserver: تم إنشاء إشعار للمستخدم', [
                        'user_id' => $user->id,
                        'user_name' => $user->name,
                        'notification_id' => $notification->id
                    ]);

                } catch (\Exception $e) {
                    \Log::error('ClientObserver: خطأ في إنشاء إشعار للمستخدم', [
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

        } catch (\Exception $e) {
            \Log::error('ClientObserver: خطأ عام في Observer', [
                'client_id' => $client->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle the clients "updated" event.
     */
    public function updated(clients $client): void
    {
        // إنشاء إشعار عند تحديث بيانات العميل
        $users = User::whereIn('role', ['super', 'admin'])->get();

        foreach ($users as $user) {
            if (NotificationSetting::isEnabledForUser($user->id, 'new_client')) {
                Notification::createNotification([
                    'type' => 'client_updated',
                    'title' => 'تحديث بيانات عميل',
                    'message' => "تم تحديث بيانات العميل: {$client->name}",
                    'icon' => 'fa-user-edit',
                    'color' => 'info',
                    'user_id' => $user->id,
                    'related_id' => $client->id,
                    'related_type' => 'App\Models\clients',
                    'data' => [
                        'client_name' => $client->name,
                        'updated_by' => Auth::user()->name ?? 'النظام'
                    ]
                ]);
            }
        }
    }

    /**
     * Handle the clients "deleted" event.
     */
    public function deleted(clients $client): void
    {
        // إنشاء إشعار عند حذف العميل
        $users = User::whereIn('role', ['super', 'admin'])->get();

        foreach ($users as $user) {
            Notification::createNotification([
                'type' => 'client_deleted',
                'title' => 'حذف عميل',
                'message' => "تم حذف العميل: {$client->name}",
                'icon' => 'fa-user-times',
                'color' => 'danger',
                'user_id' => $user->id,
                'related_id' => $client->id,
                'related_type' => 'App\Models\clients',
                'data' => [
                    'client_name' => $client->name,
                    'deleted_by' => Auth::user()->name ?? 'النظام'
                ]
            ]);
        }
    }
}
