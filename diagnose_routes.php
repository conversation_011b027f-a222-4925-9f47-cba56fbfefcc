<?php

/**
 * تشخيص مشكلة المسارات
 */

echo "🔍 تشخيص مشكلة المسارات...\n\n";

// 1. التحقق من وجود ملف routes/web.php
echo "1️⃣ التحقق من ملف المسارات...\n";
if (file_exists('routes/web.php')) {
    echo "✅ ملف routes/web.php موجود\n";
    
    // قراءة محتوى الملف
    $content = file_get_contents('routes/web.php');
    
    // البحث عن مسارات الإشعارات
    if (strpos($content, 'notifications.index') !== false) {
        echo "✅ مسار notifications.index موجود في الملف\n";
    } else {
        echo "❌ مسار notifications.index غير موجود في الملف\n";
    }
    
    if (strpos($content, 'NotificationController') !== false) {
        echo "✅ NotificationController مرجع في المسارات\n";
    } else {
        echo "❌ NotificationController غير مرجع في المسارات\n";
    }
    
} else {
    echo "❌ ملف routes/web.php غير موجود\n";
}
echo "\n";

// 2. التحقق من وجود Controller
echo "2️⃣ التحقق من Controller...\n";
if (file_exists('app/Http/Controllers/NotificationController.php')) {
    echo "✅ NotificationController موجود\n";
    
    // التحقق من وجود دالة index
    $controllerContent = file_get_contents('app/Http/Controllers/NotificationController.php');
    if (strpos($controllerContent, 'public function index()') !== false) {
        echo "✅ دالة index موجودة في Controller\n";
    } else {
        echo "❌ دالة index غير موجودة في Controller\n";
    }
    
} else {
    echo "❌ NotificationController غير موجود\n";
}
echo "\n";

// 3. التحقق من namespace
echo "3️⃣ التحقق من namespace...\n";
if (file_exists('app/Http/Controllers/NotificationController.php')) {
    $controllerContent = file_get_contents('app/Http/Controllers/NotificationController.php');
    if (strpos($controllerContent, 'namespace App\Http\Controllers;') !== false) {
        echo "✅ namespace صحيح في Controller\n";
    } else {
        echo "❌ namespace غير صحيح في Controller\n";
    }
}
echo "\n";

// 4. إنشاء مسار بديل مؤقت
echo "4️⃣ إنشاء مسار بديل مؤقت...\n";

$tempRoute = "<?php

// مسار مؤقت للاختبار
Route::get('/test-notifications', function() {
    return 'نظام الإشعارات يعمل!';
})->name('test.notifications');

Route::get('/notifications-test', function() {
    try {
        \$notifications = collect([
            (object)['id' => 1, 'title' => 'اختبار', 'message' => 'رسالة اختبار', 'is_read' => false, 'created_at' => now()]
        ]);
        
        return view('notifications.index', compact('notifications'));
    } catch (Exception \$e) {
        return 'خطأ: ' . \$e->getMessage();
    }
})->middleware('auth');
";

file_put_contents('routes/temp_notifications.php', $tempRoute);
echo "✅ تم إنشاء مسار مؤقت في routes/temp_notifications.php\n";
echo "💡 أضف هذا السطر في routes/web.php: require __DIR__.'/temp_notifications.php';\n";
echo "\n";

// 5. إنشاء controller بسيط للاختبار
echo "5️⃣ إنشاء controller بسيط للاختبار...\n";

$simpleController = "<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class SimpleNotificationController extends Controller
{
    public function index()
    {
        return view('notifications.simple', [
            'message' => 'نظام الإشعارات يعمل بشكل أساسي!'
        ]);
    }
}
";

file_put_contents('app/Http/Controllers/SimpleNotificationController.php', $simpleController);
echo "✅ تم إنشاء SimpleNotificationController\n";
echo "\n";

// 6. إنشاء view بسيط
echo "6️⃣ إنشاء view بسيط...\n";

if (!is_dir('resources/views/notifications')) {
    mkdir('resources/views/notifications', 0755, true);
}

$simpleView = "@extends('layouts.master')

@section('title')
اختبار الإشعارات
@stop

@section('content')
<div class='container'>
    <div class='alert alert-success'>
        <h4>✅ نظام الإشعارات يعمل!</h4>
        <p>{{ \$message ?? 'تم تحميل الصفحة بنجاح' }}</p>
        <hr>
        <p><strong>الخطوات التالية:</strong></p>
        <ul>
            <li>تشغيل الهجرات: <code>php artisan migrate</code></li>
            <li>مسح cache: <code>php artisan route:clear</code></li>
            <li>اختبار المسار: <a href='/test-notifications'>/test-notifications</a></li>
        </ul>
    </div>
</div>
@endsection
";

file_put_contents('resources/views/notifications/simple.blade.php', $simpleView);
echo "✅ تم إنشاء view بسيط في resources/views/notifications/simple.blade.php\n";
echo "\n";

echo "🎯 خطوات الإصلاح المقترحة:\n";
echo "1. تشغيل: php artisan route:clear\n";
echo "2. تشغيل: php artisan config:clear\n";
echo "3. تشغيل: composer dump-autoload\n";
echo "4. إضافة المسار المؤقت إلى routes/web.php\n";
echo "5. زيارة: /test-notifications\n";
echo "6. إذا عمل، زيارة: /notifications-test\n";
echo "\n";

echo "📝 إضافة هذا السطر في نهاية routes/web.php:\n";
echo "require __DIR__.'/temp_notifications.php';\n";
echo "\n";

echo "🔧 تم الانتهاء من التشخيص!\n";

?>
