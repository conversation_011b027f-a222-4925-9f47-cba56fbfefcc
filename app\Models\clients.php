<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class clients extends Model
{


    protected $table = "clients";
    protected $fillable = [



        'name',
        'gender',
        'phone',
        'subscroptiondate',
        'note',
        'password',
        'roles_name',
        'active',
        'status',
        'remember_token',



    ];
    public $timestamps = true;

    public function visits()
    {
        return $this->hasMany(visits::class,'clients_id');
    }


    public function moneys()
    {
        return $this->hasMany(moneys::class,'clients_id');
    }


    public function estps()
    {
        return $this->hasMany(estps::class,'clients_id');
    }



}
