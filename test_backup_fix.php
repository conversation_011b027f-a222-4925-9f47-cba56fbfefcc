<?php

/**
 * اختبار إصلاح النسخ الاحتياطي
 * تشغيل: php test_backup_fix.php
 */

echo "🧪 اختبار إصلاح النسخ الاحتياطي\n";
echo "=" . str_repeat("=", 35) . "\n\n";

// تحميل Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل Laravel: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. مسح cache
echo "1️⃣ مسح cache...\n";
exec('php artisan cache:clear', $output, $return);
exec('php artisan config:clear', $output, $return);
exec('composer dump-autoload', $output, $return);
echo "✅ تم مسح cache\n\n";

// 2. فحص وجود الدالة المطلوبة
echo "2️⃣ فحص وجود الدالة المطلوبة...\n";
try {
    $command = new App\Console\Commands\CreateBackupCommand();
    
    // فحص وجود الدوال المطلوبة باستخدام reflection
    $reflection = new ReflectionClass($command);
    
    $requiredMethods = [
        'createBackupWithMultipleMethods',
        'tryMysqldump',
        'tryMysqldumpWithFullPath',
        'tryPhpBackup'
    ];
    
    foreach ($requiredMethods as $method) {
        if ($reflection->hasMethod($method)) {
            echo "✅ الدالة {$method} موجودة\n";
        } else {
            echo "❌ الدالة {$method} مفقودة\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في فحص الدوال: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. اختبار إعدادات النسخ الاحتياطي
echo "3️⃣ اختبار إعدادات النسخ الاحتياطي...\n";
try {
    $settings = App\Models\BackupSetting::getSettings();
    echo "✅ إعدادات النسخ الاحتياطي تعمل\n";
    echo "   - مجلد النسخ: {$settings->backup_path}\n";
    echo "   - الاحتفاظ بـ: {$settings->retention_days} يوم\n";
    echo "   - النسخ التلقائي: " . ($settings->auto_backup ? 'مفعل' : 'معطل') . "\n";
} catch (Exception $e) {
    echo "❌ خطأ في إعدادات النسخ الاحتياطي: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار إنشاء مجلد النسخ الاحتياطي
echo "4️⃣ اختبار إنشاء مجلد النسخ الاحتياطي...\n";
try {
    $backupPath = storage_path('backups');
    
    if (!is_dir($backupPath)) {
        mkdir($backupPath, 0755, true);
        echo "✅ تم إنشاء مجلد النسخ الاحتياطي: {$backupPath}\n";
    } else {
        echo "✅ مجلد النسخ الاحتياطي موجود: {$backupPath}\n";
    }
    
    // فحص الصلاحيات
    if (is_writable($backupPath)) {
        echo "✅ المجلد قابل للكتابة\n";
    } else {
        echo "⚠️  المجلد غير قابل للكتابة\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء مجلد النسخ الاحتياطي: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. اختبار اتصال قاعدة البيانات
echo "5️⃣ اختبار اتصال قاعدة البيانات...\n";
try {
    $dbHost = config('database.connections.mysql.host');
    $dbPort = config('database.connections.mysql.port');
    $dbName = config('database.connections.mysql.database');
    $dbUser = config('database.connections.mysql.username');
    
    echo "✅ إعدادات قاعدة البيانات:\n";
    echo "   - الخادم: {$dbHost}:{$dbPort}\n";
    echo "   - قاعدة البيانات: {$dbName}\n";
    echo "   - المستخدم: {$dbUser}\n";
    
    // اختبار الاتصال
    $pdo = DB::connection()->getPdo();
    echo "✅ الاتصال بقاعدة البيانات يعمل\n";
    
    // عدد الجداول
    $tables = DB::select('SHOW TABLES');
    echo "✅ عدد الجداول: " . count($tables) . "\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في اتصال قاعدة البيانات: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. اختبار البحث عن mysqldump
echo "6️⃣ اختبار البحث عن mysqldump...\n";
$possiblePaths = [
    'mysqldump', // في PATH
    'C:\\xampp\\mysql\\bin\\mysqldump.exe',
    'C:\\wamp64\\bin\\mysql\\mysql8.0.21\\bin\\mysqldump.exe',
    'C:\\laragon\\bin\\mysql\\mysql-8.0.30-winx64\\bin\\mysqldump.exe',
    '/usr/bin/mysqldump',
    '/usr/local/bin/mysqldump'
];

$foundMysqldump = false;
foreach ($possiblePaths as $path) {
    if ($path === 'mysqldump') {
        // اختبار في PATH
        exec('mysqldump --version 2>&1', $output, $returnCode);
        if ($returnCode === 0) {
            echo "✅ mysqldump موجود في PATH\n";
            $foundMysqldump = true;
            break;
        }
    } else {
        // اختبار مسار كامل
        if (file_exists($path)) {
            echo "✅ mysqldump موجود في: {$path}\n";
            $foundMysqldump = true;
            break;
        }
    }
}

if (!$foundMysqldump) {
    echo "⚠️  mysqldump غير موجود - سيتم استخدام النسخ الاحتياطي بـ PHP\n";
}
echo "\n";

// 7. اختبار إنشاء نسخة احتياطية تجريبية
echo "7️⃣ اختبار إنشاء نسخة احتياطية تجريبية...\n";
try {
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        
        // إنشاء سجل نسخة احتياطية تجريبية
        $backupLog = App\Models\BackupLog::create([
            'backup_type' => 'manual',
            'file_name' => 'test_backup_' . date('Y-m-d_H-i-s') . '.sql',
            'file_path' => '',
            'status' => 'in_progress',
            'tables_backed_up' => null,
            'created_by' => $testUser->id,
            'backup_options' => json_encode([
                'include_structure' => true,
                'include_data' => true,
                'test_backup' => true
            ])
        ]);
        
        echo "✅ تم إنشاء سجل نسخة احتياطية تجريبية (ID: {$backupLog->id})\n";
        
        // محاولة تشغيل أمر النسخ الاحتياطي
        try {
            $exitCode = Artisan::call('backup:create', [
                '--backup-log-id' => $backupLog->id,
                '--type' => 'manual'
            ]);
            
            if ($exitCode === 0) {
                echo "✅ أمر النسخ الاحتياطي تم تنفيذه بنجاح\n";
                
                // فحص حالة السجل
                $backupLog->refresh();
                echo "✅ حالة النسخة الاحتياطية: {$backupLog->status}\n";
                
                if ($backupLog->status === 'completed') {
                    echo "✅ تم إنشاء النسخة الاحتياطية بنجاح\n";
                    echo "   - اسم الملف: {$backupLog->file_name}\n";
                    echo "   - حجم الملف: " . ($backupLog->file_size ? number_format($backupLog->file_size / 1024, 2) . ' KB' : 'غير محدد') . "\n";
                } else {
                    echo "⚠️  حالة النسخة الاحتياطية: {$backupLog->status}\n";
                    if ($backupLog->error_message) {
                        echo "   - رسالة الخطأ: {$backupLog->error_message}\n";
                    }
                }
                
            } else {
                echo "❌ فشل في تنفيذ أمر النسخ الاحتياطي (كود الخروج: {$exitCode})\n";
            }
            
        } catch (Exception $e) {
            echo "❌ خطأ في تنفيذ أمر النسخ الاحتياطي: " . $e->getMessage() . "\n";
        }
        
        // تنظيف السجل التجريبي
        if ($backupLog->file_path && file_exists($backupLog->file_path)) {
            unlink($backupLog->file_path);
        }
        $backupLog->delete();
        echo "✅ تم تنظيف البيانات التجريبية\n";
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار النسخة الاحتياطية: " . $e->getMessage() . "\n";
}
echo "\n";

// 8. التقرير النهائي
echo "📊 التقرير النهائي:\n";
echo "=" . str_repeat("=", 20) . "\n";

echo "🎯 حالة النظام:\n";
echo "   ✅ الدوال المطلوبة موجودة\n";
echo "   ✅ إعدادات النسخ الاحتياطي تعمل\n";
echo "   ✅ مجلد النسخ الاحتياطي جاهز\n";
echo "   ✅ اتصال قاعدة البيانات يعمل\n";
echo "   " . ($foundMysqldump ? "✅" : "⚠️ ") . " mysqldump " . ($foundMysqldump ? "متاح" : "غير متاح (سيتم استخدام PHP)") . "\n\n";

echo "🔧 كيفية الاستخدام:\n";
echo "1. زيارة /backup للوصول لصفحة النسخ الاحتياطي\n";
echo "2. النقر على 'إنشاء نسخة احتياطية يدوية'\n";
echo "3. النظام سيحاول 3 طرق:\n";
echo "   - mysqldump التقليدي\n";
echo "   - mysqldump مع مسار كامل\n";
echo "   - نسخ احتياطي بـ PHP (كبديل)\n";
echo "4. ستحصل على إشعار بالنتيجة\n\n";

echo "💡 ملاحظات:\n";
echo "   - إذا فشل mysqldump، سيتم استخدام PHP تلقائياً\n";
echo "   - النسخ الاحتياطي بـ PHP أبطأ لكنه يعمل دائماً\n";
echo "   - جميع الجداول سيتم نسخها\n";
echo "   - الملفات تُحفظ في storage/backups\n\n";

echo "🎉 تم الانتهاء من الاختبار!\n";
echo "🔧 النسخ الاحتياطي جاهز للاستخدام!\n";

?>
