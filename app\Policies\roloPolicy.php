<?php

namespace App\Policies;

use App\Models\User;

class roloPolicy
{
    /**
     * Create a new policy instance.
     */
    public function __construct()
    {

    }  //


public function IsSuper(user $user)
{
return $user->role === 'super';
}


public function IsNotSuper(user $user)
{
return $user->role !== 'super';
}


public function IsAdmin(user $user)
{
return $user->role === 'admin';
}


public function IsUser(user $user)
{
    return $user->role === 'user';
}

public function IsSoperAndAdmin(user $user)
{
    return $user->role === 'super' || $user->role === 'admin';
}


public function IsUserAndSecretary(user $user)
{
    return $user->role === 'user' || $user->role === 'admin';
}




}
