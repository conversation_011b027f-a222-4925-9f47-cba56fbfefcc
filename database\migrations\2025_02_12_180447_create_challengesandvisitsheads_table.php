<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('challengesandvisitsheads', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('visit_id');
             $table->unsignedBigInteger('challenge_id');
             $table->foreign('visit_id')->references('id')->on('visits');
             $table->foreign('challenge_id')->references('id')->on('challenges');
             $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('challengesandvisitsheads');
    }
};
