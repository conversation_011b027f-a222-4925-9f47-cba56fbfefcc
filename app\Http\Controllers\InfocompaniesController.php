<?php

namespace App\Http\Controllers;

use App\Models\infocompanies;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Facade;
use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Storage;
use PhpParser\Node\Stmt\Return_;

class InfocompaniesController extends Controller
{

use AuthorizesRequests;
    /**
     * Display a listing of the resource.
     */


       public function index(user $user)
    {
        $this->authorize('IsSuper',$user);

          $infocompanies = infocompanies::all();

        return view('infocompanies.infocompanies', compact('infocompanies'));
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(infocompanies $infocompanies)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(infocompanies $infocompanies)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */

    // public function update(Request $request)

    public function update(Request $request)

    {


        $id = $request->id;



  $validatedData = $request->validate([


    'nameofcompany' => 'required:infocompanies|max:255',
    'nameofcompanymanger' => 'required:infocompanies|max:255',
    'nameofcompanyurlmon' => 'required:infocompanies|max:255',
    'titleofcompany' => 'required:infocompanies|max:255',
    'detailsofcompany' => 'required:infocompanies|max:255',
    'dateofinsertofcompany' => 'required:infocompanies|max:255',
    'langueofcompany' => 'required:infocompanies|max:255',
    'emailofcompany' => 'required:infocompanies|max:255',
    'phoneofcompany' => 'required:clients|max:255|digits:9|regex:/[0-9]{9}/',
    'phone2ofcompany' => 'required:clients|max:255|digits:9|regex:/[0-9]{9}/',
    'urlmon' => 'required:infocompanies|max:255',
    'nameofprogrammarmon' => 'required:infocompanies|max:255',



        ],[

            'nameofcompany.required' =>'يرجي ادخال اسم الشركة',
            'nameofcompanymanger.required' =>'يرجي ادخال اسم المدير',
            'nameofcompanyurlmon.required' =>'يرجي ادخال الرابط',
            'titleofcompany.required' =>'يرجي ادخال عناوين الصفحات',
            'detailsofcompany.required' =>'يرجي ادخال تفاصيل الموقع',
            'dateofinsertofcompany.required' =>'يرجي ادخال تاريخ انشاء البرنامج للشركة',
            'emailofcompany.required' =>'يرجي ادخال الايميل',
            'phoneofcompany.required' =>'يرجي ادخال رقم الهاتف',
            'phone2ofcompany.required' =>'يرجي ادخال رقم الهاتف',
            'urlmon.required' =>'يرجي ادخال رابط المصمم',
            'nameofprogrammarmon.required' =>'يرجي ادخال اسم المصمم',


            'phoneofcompany.regex' =>'يجب ان يكون الهاتف عبارة عن ارقام فقط',
            'phoneofcompany.digits' =>'عدد ارقام الهاتف خاطيء',

            'phone2ofcompany.regex' =>'يجب ان يكون الهاتف2 عبارة عن ارقام فقط',
            'phone2ofcompany.digits' =>'الهاتف 2 عدد ارقام الهاتف خاطيء',



        ]);




        $infocompanies = infocompanies::find($id);
        $infocompanies->update([
            'nameofcompany' => $request->nameofcompany,
            'nameofcompanymanger' => $request->nameofcompanymanger,
            'nameofcompanyurlmon' => $request->nameofcompanyurlmon,
            'titleofcompany' => $request->titleofcompany,
            'detailsofcompany' => $request->detailsofcompany,
            'dateofinsertofcompany' => $request->dateofinsertofcompany,
            'langueofcompany' => $request->langueofcompany,
            'emailofcompany' => $request->emailofcompany,
            'imagepathofcompany' => $request->imagepathofcompany,
            'phoneofcompany' => $request->phoneofcompany,
            'phone2ofcompany' => $request->phone2ofcompany,
            'nameofprogrammarmon' => $request->nameofprogrammarmon,







        ]);




        session()->flash('edit','تم تعديل المعلومات بنجاج');
        return redirect()->back();

        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {
       $id = $request->id;
       User::find($id)->delete();
       session()->flash('delete','تم حذف المستخدم بنجاح');
       return redirect()->back();

    }



        public function info(Request $request,user $user)
        {
            $this->authorize('IsSuper',$user);

        $infocompanies = infocompanies::all();

        return view('infocompanies.infocompaniesphoto', compact('infocompanies'));

    }





    public function updateinfoimage(Request $request)
    {
        $id = $request->id;
        $soso =$request->imagepathofcompany;
        Storage::delete($soso);
               $validate = $request->validate([

            'imagepathofcompanyup' => 'required|image',

        ]);



        if ($request->hasFile('imagepathofcompanyup')) {
            $imagename = $request->file('imagepathofcompanyup')->getClientOriginalName();
            $imagepath = $request->file('imagepathofcompanyup')->storeAs('imagepathofcompany', $imagename);
            $slug = Str::slug($imagename, '-');
            // return $imagename;
        }


        try {


            $infocompanies = infocompanies::find($id);
            $infocompanies->update([

                'imagepathofcompany'=>$imagepath,
                'slugimagepathofcompany'=>$slug,
            ]);
           //     return redirect()->back();
           session()->flash('edit','تم تعديل الصورة بنجاج');
           return redirect()->back();

        } catch (Exception $e) {
            return $e->getMessage();
        }
    }



    // public function viewimg(Request $request)
    // {

    //     $bookdetails = infocompanies::find($request->id);

    //     return view('bookpage', compact('bookdetails'));
    // }
    // public function viewimgslug($slug)
    // {
    //     $bookdetails = infocompanies::where('slug', $slug)->first();
    //     return view('bookpage', compact('bookdetails'));
    // }






    public function act(Request $request,user $user)
    {
        $this->authorize('IsSuper',$user);

        $infocompanies = infocompanies::all();

        return view('infocompanies.act', compact('infocompanies'));

    }









    public function actupdate(Request $request)
    {
        // dd($request->all());

        if ($request->id==='1')
        {
        $id = "$request->id";
         }
  $validatedData = $request->validate([



    'dateofinsertofactive' => 'required:infocompanies|max:255',
    'dateofendofactive' => 'required:infocompanies|max:255',


        ],[

            'dateofinsertofactive.required' =>'يرجي ادخال التاريخ',
            'dateofendofactive.required' =>'يرجي ادخال التاريخ',


        ]);


        $infocompanies = infocompanies::find($id);
        $infocompanies->update([

            'dateofinsertofactive' => $request->dateofinsertofactive,
            'dateofendofactive' => $request->dateofendofactive,

        ]);




        session()->flash('edit','تم تعديل المعلومات بنجاج');
        return redirect()->back();

        //
    }



        //session()->flash('edit','تم تعديل الصورة بنجاج');
        // return redirect()->back();

         public function usersedit(Request $request,user $user)
        // public function usersedit(user $user)
        {


            $this->authorize('IsSuper',$user);
            // $usersedit = user::all();

            $usersedit = user::where('role', '!=',"super")->get();




            return view('usersedit.usersedit', compact('usersedit'));

        }










        public function usereditupdat($id)
        {


     // $money = money::where('clients_id',$id)->get()->sortByDesc("id");

     $user = User::where('id',$id)->get()->sortBy('id');


            return view('usersedit.usereditupdat', compact('user'));


        }










        public function usereditupdatwin(Request $request,user $user)

        {

            $id = $request->id;

      $validatedData = $request->validate([

        'name' => 'required|max:255|unique:users,name,'.$id,
        'email' => 'required|max:255|unique:users,email,'.$id,
        'role' => 'required:users|max:255',


            ] ,[

                'name.required' =>'يرجي ادخال الاسمٍ',
                'email.required' =>'يرجي ادخال الايميل',
                'role.required' =>'يرجي ادخال الصلاحية',




            ]);

            // return $validatedData;


            $users = user::find($id);

            $users->update([

                'name' => $request->name,
                'email' => $request->email,
                'role' => $request->role,

            ]);




            session()->flash('edit','تم تعديل المعلومات بنجاج');
            return redirect()->back();

            //
        }


    }



