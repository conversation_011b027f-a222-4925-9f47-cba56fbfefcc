<?php


use Illuminate\Support\Facades\Auth;


use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\KakaController;
use App\Http\Controllers\QuesController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\Auth\Controller;
use App\Http\Controllers\EstpsController;
use App\Http\Controllers\MoneysController;
use App\Http\Controllers\VisitsController;
use App\Http\Controllers\ProfileController;
use \App\Http\Controllers\ClientsController;
use App\Http\Controllers\ChallengesController;
use App\Http\Controllers\InfocompaniesController;
use App\Http\Controllers\SubscriptionsController;
use App\Http\Controllers\ClientschallengesController;
use App\Http\Controllers\ClientssubscriptionsController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use Symfony\Component\Routing\Exception\RouteNotFoundException;


Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');






Route::resource('subscriptions', SubscriptionsController::class);
Route::resource('clients', ClientsController::class);
Route::resource('estps', EstpsController::class);
Route::resource('ques', QuesController::class);
Route::resource('challenges', ChallengesController::class);
Route::resource('visits', VisitsController::class);
Route::resource('moneys', MoneysController::class);
Route::resource('clientschallenges', ClientschallengesController::class);
Route::resource('clientssubscriptions', ClientssubscriptionsController::class);
Route::resource('infocompanies', InfocompaniesController::class);

// مسارات الإشعارات
Route::middleware('auth')->group(function () {
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::get('/notifications/unread', [App\Http\Controllers\NotificationController::class, 'getUnread'])->name('notifications.unread');
    Route::post('/notifications/{id}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::delete('/notifications/{id}', [App\Http\Controllers\NotificationController::class, 'delete'])->name('notifications.delete');
    Route::post('/notifications/delete-read', [App\Http\Controllers\NotificationController::class, 'deleteRead'])->name('notifications.delete-read');
    Route::get('/notifications/settings', [App\Http\Controllers\NotificationController::class, 'settings'])->name('notifications.settings');
    Route::post('/notifications/settings', [App\Http\Controllers\NotificationController::class, 'updateSettings'])->name('notifications.settings.update');
    Route::get('/notifications/settings/get', [App\Http\Controllers\NotificationController::class, 'getSettings'])->name('notifications.settings.get');
    Route::post('/notifications/test-sound', [App\Http\Controllers\NotificationController::class, 'testSound'])->name('notifications.test-sound');
    Route::post('/notifications/test', [App\Http\Controllers\NotificationController::class, 'createTestNotification'])->name('notifications.test');
});

// مسارات النسخ الاحتياطي (للمدير العام فقط)
Route::middleware(['auth'])->group(function () {
    Route::get('/backup', [App\Http\Controllers\BackupController::class, 'index'])->name('backup.index');
    Route::post('/backup/settings', [App\Http\Controllers\BackupController::class, 'updateSettings'])->name('backup.settings.update');
    Route::post('/backup/create', [App\Http\Controllers\BackupController::class, 'createManualBackup'])->name('backup.create');
    Route::get('/backup/{id}/download', [App\Http\Controllers\BackupController::class, 'downloadBackup'])->name('backup.download');
    Route::delete('/backup/{id}', [App\Http\Controllers\BackupController::class, 'deleteBackup'])->name('backup.delete');
    Route::post('/backup/clean-old', [App\Http\Controllers\BackupController::class, 'cleanOldBackups'])->name('backup.clean-old');
    Route::get('/backup/status/{id}', [App\Http\Controllers\BackupController::class, 'getBackupStatus'])->name('backup.status');
    Route::post('/backup/test-connection', [App\Http\Controllers\BackupController::class, 'testBackupConnection'])->name('backup.test-connection');
});

//route::get('/loveu', [InfocompaniesController::class, 'loveu']);


################################################3

route::get('/infocompaniesphoto', [InfocompaniesController::class, 'info']);


route::get('/act', [InfocompaniesController::class, 'act']);

route::get('usersedit/usersedit', [InfocompaniesController::class, 'usersedit']);

route::post('/infocompanies/actupdate', [InfocompaniesController::class, 'actupdate']);


route::post('/infocompaniesphoto/updateinfoimage', [InfocompaniesController::class, 'updateinfoimage']);


// route::post('viewimg/{slug}', [InfocompaniesController::class, 'viewbook']);
// route::get('viewimg/{slug}', [InfocompaniesController::class, 'viewbookslug']);


################################################
Route::get('/usersedit/usereditupdat/{id}', 'App\Http\Controllers\InfocompaniesController@usereditupdat');


route::post('/infocompanies/usereditupdatwin/{id}', action: [InfocompaniesController::class, 'usereditupdatwin']);
################################################


//route::post('/infocompanies/usereditupdat', [InfocompaniesController::class, 'usereditupdatwin']);
################################################


Route::get('/estpsdetals/{id}', 'App\Http\Controllers\EstpsController@edit');
Route::get('/estpsdetalsnoe/{id}', 'App\Http\Controllers\EstpsController@editnoe');
Route::get('/clientschallengesb/{id}', 'App\Http\Controllers\ClientschallengesController@edit');


Route::resource('/', HomeController::class);
Route::resource('home', HomeController::class);
 Route::resource('index', HomeController::class);

 Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])
                ->middleware('auth')
             ->name('logout');


});



require __DIR__.'/auth.php';
