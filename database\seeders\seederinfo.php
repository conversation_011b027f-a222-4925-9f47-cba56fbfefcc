<?php

namespace Database\Seeders;

use App\Models\infocompanies;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class seederinfo extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        infocompanies::create([
                'nameofcompany' => 'nameofcompany',
                'nameofcompanymanger' => 'nameofcompanymanger',
                'nameofcompanyurlmon' => 'nameofcompanyurlmon',
                'titleofcompany' => 'titleofcompany',
                'detailsofcompany' => 'detailsofcompany',
                'dateofinsertofcompany' => '2025-04-15',
                'langueofcompany' => 'arabic',
                'imagepathofcompany' => 'imagepathofcompany',
                'slugimagepathofcompany' => 'null',
                'emailofcompany' => '<EMAIL>',
                'phoneofcompany' => '*********',
                'phone2ofcompany' => '*********',
                'nameofprogrammarmon' => 'nameofprogrammarmon',
                'phonenameofprogrammarmon' => '*********',
                'urlmon' => 'urlmon',
                'dateofinsertofactive' => '2025-04-15',
                'dateofendofactive' => '2025-09-15',
                'updated_at' => '2025-11-15',
                'created_at' => '2025-09-15',



        ]);

        //
    }
}



