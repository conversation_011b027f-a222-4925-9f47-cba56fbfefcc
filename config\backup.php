<?php

return [
    /*
    |--------------------------------------------------------------------------
    | إعدادات نظام النسخ الاحتياطي
    |--------------------------------------------------------------------------
    |
    | هذا الملف يحتوي على جميع إعدادات نظام النسخ الاحتياطي المتقدم
    |
    */

    'enabled' => env('BACKUP_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | إعدادات قاعدة البيانات
    |--------------------------------------------------------------------------
    */
    'database' => [
        'connection' => env('DB_CONNECTION', 'mysql'),
        'host' => env('DB_HOST', '127.0.0.1'),
        'port' => env('DB_PORT', '3306'),
        'database' => env('DB_DATABASE', 'forge'),
        'username' => env('DB_USERNAME', 'forge'),
        'password' => env('DB_PASSWORD', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | مسارات النسخ الاحتياطي
    |--------------------------------------------------------------------------
    */
    'paths' => [
        'local' => storage_path('app/backups'),
        'temp' => storage_path('app/temp'),
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الضغط
    |--------------------------------------------------------------------------
    */
    'compression' => [
        'enabled' => true,
        'level' => 9, // مستوى الضغط (1-9)
        'method' => 'gzip'
    ],

    /*
    |--------------------------------------------------------------------------
    | الجداول الافتراضية للنسخ الاحتياطي
    |--------------------------------------------------------------------------
    */
    'default_tables' => [
        'users',
        'clients',
        'visits',
        'moneys',
        'challenges',
        'subscriptions',
        'estps',
        'hed_estps',
        'clientschallenges',
        'clientssubscriptions',
        'historycls',
        'infocompanies',
        'ques',
        'notifications',
        'notification_settings'
    ],

    /*
    |--------------------------------------------------------------------------
    | جداول النظام (لا يُنصح بنسخها)
    |--------------------------------------------------------------------------
    */
    'system_tables' => [
        'migrations',
        'password_resets',
        'failed_jobs',
        'personal_access_tokens'
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الجدولة
    |--------------------------------------------------------------------------
    */
    'schedule' => [
        'daily' => [
            'enabled' => true,
            'time' => '02:00',
            'keep_days' => 7
        ],
        'weekly' => [
            'enabled' => true,
            'day' => 5, // الجمعة
            'time' => '03:00',
            'keep_weeks' => 4
        ],
        'monthly' => [
            'enabled' => true,
            'day' => 1, // أول يوم في الشهر
            'time' => '04:00',
            'keep_months' => 12
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التنظيف
    |--------------------------------------------------------------------------
    */
    'cleanup' => [
        'enabled' => true,
        'keep_days' => 30,
        'keep_minimum' => 5, // الحد الأدنى من النسخ للاحتفاظ بها
        'auto_cleanup' => true
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الأمان
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encrypt_backups' => false,
        'encryption_key' => env('BACKUP_ENCRYPTION_KEY'),
        'verify_integrity' => true,
        'secure_delete' => true
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الإشعارات
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'enabled' => true,
        'on_success' => true,
        'on_failure' => true,
        'recipients' => ['super', 'admin']
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات الأداء
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'memory_limit' => '512M',
        'time_limit' => 300, // 5 دقائق
        'chunk_size' => 1000,
        'parallel_processing' => false
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات التخزين الخارجي
    |--------------------------------------------------------------------------
    */
    'external_storage' => [
        'enabled' => false,
        'driver' => 's3', // s3, ftp, sftp
        'config' => [
            // إعدادات التخزين السحابي
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | إعدادات السجلات
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => true,
        'level' => 'info',
        'keep_logs_days' => 90,
        'detailed_logging' => true
    ],

    /*
    |--------------------------------------------------------------------------
    | أوامر ما قبل وما بعد النسخ الاحتياطي
    |--------------------------------------------------------------------------
    */
    'hooks' => [
        'before_backup' => [
            // أوامر تنفذ قبل النسخ الاحتياطي
        ],
        'after_backup' => [
            // أوامر تنفذ بعد النسخ الاحتياطي
        ],
        'on_failure' => [
            // أوامر تنفذ عند فشل النسخ الاحتياطي
        ]
    ]
];
