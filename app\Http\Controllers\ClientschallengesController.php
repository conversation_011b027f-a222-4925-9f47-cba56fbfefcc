<?php

namespace App\Http\Controllers;

use App\Models\clients;
use App\Models\challenges;
use App\Models\historycls;
use Illuminate\Http\Request;
use App\Models\clientschallenges;

class ClientschallengesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $validatedData = $request->validate([
            'id' => 'required:clientschallenges',
        ], [

            'id.required' => 'يرجي ادخال اسم التحدي',
            'id.unique' => 'اسم التحدي مسجل مسبقا',


        ]);



        $clientschallengeshistory = clientschallenges::create([
            'clients_id' => $request->clients_id,
            'tahadde_id' => $request->id,
            'startdateofcustomerchallenges' => $request->startdate,
            'endtdateofcustomerchallenges' => $request->timeenddate,

        ]);

        historycls::create([
            'clientschallenges_id' => $clientschallengeshistory->id,
            'clients_id' => $request->clients_id,
             'challenges_id'=> $request->id,
            'type_history' => "tahaadddy",

        ]);


        session()->flash('Add', 'تم اضافة التحدي بنجاح ');
        return redirect()->back();

        //

        //
    }

    /**
     * Display the specified resource.
     */
    public function show(clientschallenges $clientschallenges)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request)
    {
        $id = $request->id;


        $clients = clients::all();
        $clientschallenges = clientschallenges::where('tahadde_id', $id)->get();



        $challenges = challenges::where('id', $id)->first();
        return view('clientschallenges.clientschallengesb', compact('clientschallenges', 'clients', 'challenges'));
    }
    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)

    {

        $id = $request->id;


        $validate = $request->validate([



            'tahadde_id' => 'required:clientschallenges,tahadde_id,' . $id,
        ], [

            'tahadde_id.required' => 'يرجي ادخال اسم التحدي',
            'tahadde_id.unique' => 'اسم التحدي مسجل مسبقا',

        ]);

        $challenges = clientschallenges::find($id);
        $challenges->update([
            'startdateofcustomerchallenges' => $request->startdateofcustomerchallenges,
            'endtdateofcustomerchallenges' => $request->endtdateofcustomerchallenges,



        ]);

        session()->flash('edit', 'تم تعديل التحدي الخاص بنجاج');
        return redirect()->back();
    }
    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {
        $clients_idh=$request->clients_id;

        $id = $request->id;



        $historycls = historycls::where('clients_id', '=', $clients_idh)
         ->where('clientschallenges_id', '=',  $id)
         ->delete();
         clientschallenges::find($id)->delete();

        session()->flash('delete', 'تم حذف التحدي الخاص بالزبون بنجاح');
        return redirect()->back();
        //
    }
}
