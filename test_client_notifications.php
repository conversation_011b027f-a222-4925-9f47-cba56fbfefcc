<?php

/**
 * اختبار شامل لإشعارات العملاء الجدد
 * تشغيل: php test_client_notifications.php
 */

echo "🧪 اختبار شامل لإشعارات العملاء الجدد\n";
echo "=" . str_repeat("=", 45) . "\n\n";

// تحميل Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل Laravel: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. فحص Observer
echo "1️⃣ فحص Observer...\n";
try {
    $providerContent = file_get_contents('app/Providers/AppServiceProvider.php');
    if (strpos($providerContent, 'clients::observe(ClientObserver::class)') !== false) {
        echo "✅ Observer مسجل في AppServiceProvider\n";
    } else {
        echo "❌ Observer غير مسجل في AppServiceProvider\n";
        echo "💡 أضف هذا السطر في boot() method:\n";
        echo "   clients::observe(ClientObserver::class);\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص Observer: " . $e->getMessage() . "\n";
}
echo "\n";

// 2. فحص المستخدمين المستهدفين
echo "2️⃣ فحص المستخدمين المستهدفين...\n";
try {
    $users = App\Models\User::whereIn('role', ['super', 'admin', 'secretary'])->get();
    echo "✅ عدد المستخدمين المستهدفين: " . $users->count() . "\n";
    
    foreach ($users as $user) {
        echo "   - {$user->name} ({$user->role}) - ID: {$user->id}\n";
    }
    
    if ($users->count() === 0) {
        echo "⚠️  لا يوجد مستخدمين مستهدفين!\n";
        echo "💡 تأكد من وجود مستخدمين بأدوار: super, admin, secretary\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص المستخدمين: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. اختبار إنشاء إشعار تجريبي
echo "3️⃣ اختبار إنشاء إشعار تجريبي...\n";
try {
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        echo "✅ تم تسجيل دخول المستخدم: {$testUser->name}\n";
        
        // إنشاء إشعار تجريبي مباشرة
        $notification = App\Models\Notification::createNotification([
            'type' => 'new_client',
            'title' => '👤 عميل تجريبي',
            'message' => 'تم إنشاء عميل تجريبي لاختبار النظام - ' . now()->format('H:i:s'),
            'icon' => 'fa-user-plus',
            'color' => 'success',
            'user_id' => $testUser->id,
            'data' => [
                'test' => true,
                'client_name' => 'عميل تجريبي',
                'client_phone' => '0500000000',
                'created_by' => $testUser->name,
                'created_at' => now()->format('Y-m-d H:i:s')
            ]
        ]);
        
        echo "✅ تم إنشاء إشعار تجريبي (ID: {$notification->id})\n";
        
        // فحص الإشعار
        $createdNotification = App\Models\Notification::find($notification->id);
        if ($createdNotification) {
            echo "✅ الإشعار موجود في قاعدة البيانات\n";
            echo "   - النوع: {$createdNotification->type}\n";
            echo "   - العنوان: {$createdNotification->title}\n";
            echo "   - الرسالة: {$createdNotification->message}\n";
            echo "   - مقروء: " . ($createdNotification->is_read ? 'نعم' : 'لا') . "\n";
        }
        
        // تنظيف
        $notification->delete();
        echo "✅ تم حذف الإشعار التجريبي\n";
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الإشعار التجريبي: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار إنشاء عميل جديد
echo "4️⃣ اختبار إنشاء عميل جديد...\n";
try {
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        
        // إنشاء عميل تجريبي
        $testClient = App\Models\clients::create([
            'name' => 'عميل اختبار - ' . date('H:i:s'),
            'gender' => 'male',
            'phone' => '59' . rand(1000000, 9999999),
            'subscroptiondate' => now()->toDateString(),
            'note' => 'عميل تجريبي لاختبار Observer',
            'password' => '123456',
            'roles_name' => 'yes',
            'active' => 'yes',
            'status' => 'active'
        ]);
        
        echo "✅ تم إنشاء عميل تجريبي: {$testClient->name} (ID: {$testClient->id})\n";
        
        // انتظار قليل للسماح للـ Observer بالعمل
        sleep(2);
        
        // فحص الإشعارات المُنشأة
        $notifications = App\Models\Notification::where('type', 'new_client')
                                              ->where('related_id', $testClient->id)
                                              ->get();
        
        echo "✅ عدد الإشعارات المُنشأة: " . $notifications->count() . "\n";
        
        if ($notifications->count() > 0) {
            echo "🎉 Observer يعمل بشكل صحيح!\n";
            foreach ($notifications as $notification) {
                $user = App\Models\User::find($notification->user_id);
                echo "   - إشعار للمستخدم: {$user->name} - {$notification->title}\n";
                echo "     الرسالة: {$notification->message}\n";
                echo "     مقروء: " . ($notification->is_read ? 'نعم' : 'لا') . "\n";
            }
        } else {
            echo "❌ لم يتم إنشاء أي إشعارات! Observer قد لا يعمل\n";
            
            // فحص اللوج
            echo "\n📋 فحص آخر سجلات اللوج:\n";
            $logFile = 'storage/logs/laravel.log';
            if (file_exists($logFile)) {
                $logContent = file_get_contents($logFile);
                $lines = explode("\n", $logContent);
                $recentLines = array_slice($lines, -10);
                
                foreach ($recentLines as $line) {
                    if (strpos($line, 'ClientObserver') !== false) {
                        echo "   " . $line . "\n";
                    }
                }
            }
        }
        
        // تنظيف البيانات التجريبية
        App\Models\Notification::where('related_id', $testClient->id)->delete();
        $testClient->delete();
        echo "✅ تم تنظيف البيانات التجريبية\n";
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار إنشاء العميل: " . $e->getMessage() . "\n";
    echo "تفاصيل الخطأ: " . $e->getTraceAsString() . "\n";
}
echo "\n";

// 5. فحص المسارات
echo "5️⃣ فحص المسارات...\n";
try {
    $routes = [
        'notifications.index' => '/notifications',
        'notifications.settings' => '/notifications/settings'
    ];
    
    foreach ($routes as $routeName => $expectedPath) {
        try {
            $url = route($routeName);
            echo "✅ مسار {$routeName}: {$url}\n";
        } catch (Exception $e) {
            echo "❌ مسار {$routeName}: غير موجود\n";
        }
    }
} catch (Exception $e) {
    echo "❌ خطأ في فحص المسارات: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. فحص ملفات JavaScript و CSS
echo "6️⃣ فحص ملفات الواجهة...\n";
$files = [
    'public/assets/js/notifications.js' => 'JavaScript',
    'public/assets/css/notifications.css' => 'CSS',
    'resources/views/notifications/settings.blade.php' => 'Settings View'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$description}: موجود\n";
    } else {
        echo "❌ {$description}: مفقود\n";
    }
}
echo "\n";

// 7. التقرير النهائي
echo "📊 التقرير النهائي:\n";
echo "=" . str_repeat("=", 20) . "\n";

$totalNotifications = DB::table('notifications')->count();
$clientNotifications = DB::table('notifications')->where('type', 'new_client')->count();
$unreadClientNotifications = DB::table('notifications')
    ->where('type', 'new_client')
    ->where('is_read', false)
    ->count();

echo "📈 الإحصائيات:\n";
echo "   - إجمالي الإشعارات: {$totalNotifications}\n";
echo "   - إشعارات العملاء الجدد: {$clientNotifications}\n";
echo "   - إشعارات العملاء غير المقروءة: {$unreadClientNotifications}\n\n";

echo "🎯 خطوات الاختبار الحقيقي:\n";
echo "1. زيارة /notifications/settings\n";
echo "2. تغيير وقت التحقق (افتراضي: 3 دقائق)\n";
echo "3. النقر على 'اختبار إشعار عميل جديد'\n";
echo "4. مشاهدة الإشعار في الجرس\n";
echo "5. زيارة /clients وإضافة عميل حقيقي\n";
echo "6. انتظار 3 دقائق أو تحديث الصفحة\n";
echo "7. مشاهدة الإشعار في الجرس\n\n";

echo "🔧 في حالة عدم ظهور الإشعارات:\n";
echo "   - تحقق من storage/logs/laravel.log\n";
echo "   - تأكد من تسجيل Observer في AppServiceProvider\n";
echo "   - تحقق من console المتصفح\n";
echo "   - تأكد من وجود مستخدمين بأدوار مناسبة\n\n";

echo "🎉 انتهى الاختبار الشامل!\n";

?>
