<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // نوع الإشعار (new_client, new_visit, etc.)
            $table->string('title'); // عنوان الإشعار
            $table->text('message'); // محتوى الإشعار
            $table->string('icon')->default('fa-bell'); // أيقونة الإشعار
            $table->string('color')->default('primary'); // لون الإشعار
            $table->unsignedBigInteger('user_id')->nullable(); // المستخدم المستهدف (null = للجميع)
            $table->unsignedBigInteger('related_id')->nullable(); // معرف العنصر المرتبط
            $table->string('related_type')->nullable(); // نوع العنصر المرتبط
            $table->boolean('is_read')->default(false); // هل تم قراءة الإشعار
            $table->timestamp('read_at')->nullable(); // وقت القراءة
            $table->json('data')->nullable(); // بيانات إضافية
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'is_read']);
            $table->index(['type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
