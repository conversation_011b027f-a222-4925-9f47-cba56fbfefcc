<?php

namespace App\Models;

use App\Models\visits;
use App\Models\challenges;
use App\Models\subscriptions;
use App\Models\clientschallenges;
use App\Models\clientssubscriptions;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class historycls extends Model
{

    use HasFactory;
    protected $guarded = [];


#########################################


      public function clientschallenges()
      {
        return $this->belongsTo(clientschallenges::class,'clientschallenges_id');


      }



###########################################


public function subscriptions()
{
  return $this->belongsTo(subscriptions::class,'subscriptions_id');


}



public function clientssubscriptions()
{
  return $this->belongsTo(clientssubscriptions::class,'clientssubscriptions_id');


}

###########################################


public function visits()
{
  return $this->belongsTo(visits::class,'visit_id');


}






public function challengesy()
{
  return $this->belongsTo(challenges::class,'challenges_id');


}



// challenges_id









}

