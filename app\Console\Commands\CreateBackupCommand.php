<?php

namespace App\Console\Commands;

use App\Models\BackupLog;
use App\Models\BackupSetting;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class CreateBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'backup:create 
                            {--backup-log-id= : معرف سجل النسخة الاحتياطية}
                            {--type=manual : نوع النسخة الاحتياطية}
                            {--tables=* : الجداول المراد نسخها}';

    /**
     * The console command description.
     */
    protected $description = 'إنشاء نسخة احتياطية من قاعدة البيانات';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $startTime = microtime(true);
        $backupLogId = $this->option('backup-log-id');
        $backupType = $this->option('type');
        $tables = $this->option('tables');

        // الحصول على الإعدادات
        $settings = BackupSetting::getSettings();

        // إنشاء سجل النسخة الاحتياطية إذا لم يكن موجوداً
        if (!$backupLogId) {
            $backupLog = BackupLog::create([
                'backup_type' => $backupType,
                'file_name' => 'backup_' . date('Y-m-d_H-i-s') . '.sql',
                'file_path' => '',
                'status' => 'in_progress',
                'tables_backed_up' => $tables ?: $settings->backup_tables
            ]);
        } else {
            $backupLog = BackupLog::find($backupLogId);
            if (!$backupLog) {
                $this->error('سجل النسخة الاحتياطية غير موجود');
                return 1;
            }
        }

        try {
            $this->info('بدء عملية النسخ الاحتياطي...');

            // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            $backupPath = storage_path('app/' . $settings->backup_path);
            if (!is_dir($backupPath)) {
                mkdir($backupPath, 0755, true);
            }

            // اسم الملف
            $fileName = $backupLog->file_name;
            $filePath = $backupPath . '/' . $fileName;

            // الحصول على معلومات قاعدة البيانات
            $dbHost = config('database.connections.mysql.host');
            $dbPort = config('database.connections.mysql.port');
            $dbName = config('database.connections.mysql.database');
            $dbUser = config('database.connections.mysql.username');
            $dbPassword = config('database.connections.mysql.password');

            // إنشاء أمر mysqldump
            $command = "mysqldump --host={$dbHost} --port={$dbPort} --user={$dbUser}";
            
            if ($dbPassword) {
                $command .= " --password={$dbPassword}";
            }

            $command .= " --single-transaction --routines --triggers";

            // إضافة الجداول المحددة
            $tablesToBackup = $backupLog->tables_backed_up ?: $settings->backup_tables;
            if (!empty($tablesToBackup)) {
                $command .= " {$dbName} " . implode(' ', $tablesToBackup);
            } else {
                $command .= " {$dbName}";
            }

            $command .= " > {$filePath}";

            // تنفيذ الأمر
            $this->info('تنفيذ أمر النسخ الاحتياطي...');
            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                throw new \Exception('فشل في تنفيذ أمر mysqldump. كود الخطأ: ' . $returnCode);
            }

            // التحقق من وجود الملف وحجمه
            if (!file_exists($filePath) || filesize($filePath) === 0) {
                throw new \Exception('فشل في إنشاء ملف النسخة الاحتياطية أو الملف فارغ');
            }

            $fileSize = filesize($filePath);

            // ضغط الملف إذا كان مفعلاً
            if ($settings->compress_backups) {
                $this->info('ضغط الملف...');
                $compressedPath = $filePath . '.gz';
                
                $fp = fopen($filePath, 'rb');
                $gzfp = gzopen($compressedPath, 'wb9');
                
                while (!feof($fp)) {
                    gzwrite($gzfp, fread($fp, 1024 * 512));
                }
                
                fclose($fp);
                gzclose($gzfp);
                
                // حذف الملف غير المضغوط
                unlink($filePath);
                
                // تحديث معلومات الملف
                $filePath = $compressedPath;
                $fileName = $fileName . '.gz';
                $fileSize = filesize($filePath);
            }

            // حساب المدة
            $duration = round(microtime(true) - $startTime);

            // تحديث سجل النسخة الاحتياطية
            $backupLog->update([
                'file_name' => $fileName,
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'status' => 'success',
                'duration' => $duration
            ]);

            $this->info('تم إنشاء النسخة الاحتياطية بنجاح');
            $this->info("اسم الملف: {$fileName}");
            $this->info("حجم الملف: " . $this->formatBytes($fileSize));
            $this->info("المدة: {$duration} ثانية");

            // إنشاء إشعار للمدراء
            $this->createSuccessNotification($backupLog);

            return 0;

        } catch (\Exception $e) {
            $this->error('خطأ في النسخ الاحتياطي: ' . $e->getMessage());

            // تحديث سجل النسخة الاحتياطية
            $backupLog->markAsFailed($e->getMessage());

            // إنشاء إشعار بالفشل
            $this->createFailureNotification($backupLog, $e->getMessage());

            return 1;
        }
    }

    /**
     * إنشاء إشعار نجاح النسخ الاحتياطي
     */
    private function createSuccessNotification(BackupLog $backupLog)
    {
        $users = User::whereIn('role', ['super', 'admin'])->get();

        foreach ($users as $user) {
            Notification::createNotification([
                'type' => 'backup_success',
                'title' => 'نجح النسخ الاحتياطي',
                'message' => "تم إنشاء نسخة احتياطية بنجاح ({$backupLog->backup_type})",
                'icon' => 'fa-database',
                'color' => 'success',
                'user_id' => $user->id,
                'related_id' => $backupLog->id,
                'related_type' => 'App\Models\BackupLog',
                'data' => [
                    'file_name' => $backupLog->file_name,
                    'file_size' => $backupLog->formatted_file_size,
                    'duration' => $backupLog->formatted_duration,
                    'backup_type' => $backupLog->backup_type
                ]
            ]);
        }
    }

    /**
     * إنشاء إشعار فشل النسخ الاحتياطي
     */
    private function createFailureNotification(BackupLog $backupLog, $errorMessage)
    {
        $users = User::whereIn('role', ['super', 'admin'])->get();

        foreach ($users as $user) {
            Notification::createNotification([
                'type' => 'backup_failed',
                'title' => 'فشل النسخ الاحتياطي',
                'message' => "فشل في إنشاء نسخة احتياطية ({$backupLog->backup_type})",
                'icon' => 'fa-exclamation-triangle',
                'color' => 'danger',
                'user_id' => $user->id,
                'related_id' => $backupLog->id,
                'related_type' => 'App\Models\BackupLog',
                'data' => [
                    'error_message' => $errorMessage,
                    'backup_type' => $backupLog->backup_type
                ]
            ]);
        }
    }

    /**
     * تنسيق حجم الملف
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
