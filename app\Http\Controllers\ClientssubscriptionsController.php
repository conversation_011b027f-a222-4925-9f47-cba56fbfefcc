<?php

namespace App\Http\Controllers;

use App\Models\historycls;
use Illuminate\Http\Request;
use App\Models\clientssubscriptions;

class ClientssubscriptionsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $validatedData = $request->validate([
            'id' => 'required:clientssubscriptions',
        ],[

            'id.required' =>'يرجي ادخال اسم الاشتراك',
            'id.unique' =>'اسم الاشتراك مسجل مسبقا',


        ]);

        $clientssubscriptionshistory=clientssubscriptions::create([
        'subscriptions_id' => $request->id,
        'clients_id' => $request->clients_id,
        'startdateofcustomersubscriptions' => $request->startdatesubscriptions,
        'endtdateofcustomersubscriptions' => $request->timeenddatesubscriptions,




        ]);

        historycls::create([
            'clientssubscriptions_id' => $clientssubscriptionshistory->id,
            'clients_id' => $request->clients_id,
             'subscriptions_id'=> $request->id,
            'type_history' => "eshteraak",









        ]);
        session()->flash('Add', 'تم اضافة الاشتراك بنجاح ');
        return redirect()->back();

        //

        //
    }


    /**
     * Display the specified resource.
     */
    public function show(clientssubscriptions $clientssubscriptions)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(clientssubscriptions $clientssubscriptions)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)

    {

        $id = $request->id;


        $validatedData = $request->validate([


            'subscriptions_id' => 'required:clientssubscriptions,subscriptions_id,'.$id,
        ],[

            'subscriptions_id.required' =>'يرجي ادخال اسم التحدي',
            'subscriptions_id.unique' =>'اسم التحدي مسجل مسبقا',

        ]);

        $clientssubscriptions = clientssubscriptions::find($id);
        $clientssubscriptions->update([
            'startdateofcustomersubscriptions' => $request->startdateofcustomersubscriptions,
            'endtdateofcustomersubscriptions' => $request->endtdateofcustomersubscriptions,



        ]);

        session()->flash('edit','تم تعديل الاشتراك الخاص بنجاج');
        return redirect()->back();
    }
    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request)
    {


        $clients_idh=$request->clients_id;




        $id = $request->id;

        $historycls = historycls::where('clients_id', '=', $clients_idh)
        ->where('clientssubscriptions_id', '=',  $id)
        ->delete();

        clientssubscriptions::find($id)->delete();



        session()->flash('delete','تم حذف الاشتراك الخاص بالزبون بنجاح');
        return redirect()->back();
        //

        //
    }
}
