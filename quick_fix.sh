#!/bin/bash

echo "🚀 إصلاح سريع لنظام الإشعارات والنسخ الاحتياطي..."

# مسح cache
echo "1. مسح cache..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# تحديث autoloader
echo "2. تحديث autoloader..."
composer dump-autoload

# تشغيل الهجرات
echo "3. تشغيل الهجرات..."
php artisan migrate --force

# إنشاء المجلدات المطلوبة
echo "4. إنشاء المجلدات..."
mkdir -p storage/backups
mkdir -p storage/app/backups
mkdir -p storage/app/temp
mkdir -p public/assets/sounds

# تعيين الصلاحيات
echo "5. تعيين الصلاحيات..."
chmod 755 storage/backups
chmod 755 storage/app/backups
chmod 755 storage/app/temp
chmod 755 public/assets/sounds

# اختبار المسارات
echo "6. اختبار المسارات..."
php artisan route:list --name=notifications

echo "✅ تم الانتهاء من الإصلاح السريع!"
echo "💡 جرب الآن زيارة /notifications"
