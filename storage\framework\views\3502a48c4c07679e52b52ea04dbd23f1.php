﻿
<?php $__env->startSection('title'); ?>



    <?php
    if ($clients->gender == 'male') {
        echo 'السيد';
    }
    
    if ($clients->gender == 'female') {
        echo 'السيدة';
    }
    
    ?>
    <?php echo e($clients->name); ?>


    <?php
        $infocompanies = DB::table('infocompanies')->get();
    ?>
    <?php echo e($infocompanies->implode('titleofcompany')); ?>



    
<?php $__env->stopSection(); ?>



<?php $__env->startSection('css'); ?>
    <!-- Internal Data table css -->

    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">
    <link href="<?php echo e(URL::asset('assets/plugins/prism/prism.css')); ?>" rel="stylesheet">

    <!---Internal Owl Carousel css-->
    <link href="<?php echo e(URL::asset('assets/plugins/owl-carousel/owl.carousel.css')); ?>" rel="stylesheet">
    <!---Internal  Multislider css-->
    <link href="<?php echo e(URL::asset('assets/plugins/multislider/multislider.css')); ?>" rel="stylesheet">
    <!--- Select2 css -->
    <link href="<?php echo e(URL::asset('assets/plugins/select2/css/select2.min.css')); ?>" rel="stylesheet">


<?php $__env->stopSection(); ?>
<?php $__env->startSection('page-header'); ?>
    <!-- breadcrumb -->
    <div class="breadcrumb-header justify-content-between">
        <div class="my-auto">
            <div class="d-flex">
                <h4 class="content-title mb-0 my-auto"> <?php
                if ($clients->gender == 'male') {
                    echo 'السيد';
                }
                
                if ($clients->gender == 'female') {
                    echo 'السيدة';
                }
                
                ?>:</h4><span
                    class="text-muted mt-1 tx-13 mr-2 mb-0">
                    <h2 class="text-danger">



                        <?php echo e($clients->name); ?>





                        <?php
                        if ($clients->gender == 'male') {
                            echo 'المحترم';
                        }
                        
                        if ($clients->gender == 'female') {
                            echo 'المحترمة';
                        }
                        
                        ?>
                    </h2>
                </span>


            </div>
        </div>

        <h5> رقم الهاتف:

            0<?php echo e($clients->phone); ?>


        </h5>



        <!--
                                                                                                                     <div class="d-flex my-xl-auto right-content">
                                                                                                                      <div class="pr-1 mb-3 mb-xl-0">
                                                                                                                       <button type="button" class="btn btn-info btn-icon ml-2"><i class="mdi mdi-filter-variant"></i></button>
                                                                                                                      </div>
                                                                                                                      <div class="pr-1 mb-3 mb-xl-0">
                                                                                                                       <button type="button" class="btn btn-danger btn-icon ml-2"><i class="mdi mdi-star"></i></button>
                                                                                                                      </div>
                                                                                                                      <div class="pr-1 mb-3 mb-xl-0">
                                                                                                                       <button type="button" class="btn btn-warning  btn-icon ml-2"><i class="mdi mdi-refresh"></i></button>
                                                                                                                      </div>
                                                                                                                      <div class="mb-3 mb-xl-0">
                                                                                                                       <div class="btn-group dropdown">
                                                                                                                        <button type="button" class="btn btn-primary">14 Aug 2019</button>
                                                                                                                        <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" id="dropdownMenuDate" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                                                                        <span class="sr-only">Toggle Dropdown</span>
                                                                                                                        </button>
                                                                                                                        <div class="dropdown-menu dropdown-menu-left" aria-labelledby="dropdownMenuDate" data-x-placement="bottom-end">
                                                                                                                         <a class="dropdown-item" href="#">2015</a>
                                                                                                                         <a class="dropdown-item" href="#">2016</a>
                                                                                                                         <a class="dropdown-item" href="#">2017</a>
                                                                                                                         <a class="dropdown-item" href="#">2018</a>
                                                                                                                        </div>
                                                                                                                       </div>
                                                                                                                      </div>
                                                                                                                     </div>

                                                                                                                                    -->
    </div>
    <!-- breadcrumb -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>

    <!-- row -->

    <!--/div-->

    <!--div-->


    <?php if($errors->any()): ?>
        <div class="alert alert-danger">
            <ul>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if(session()->has('Add')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong><?php echo e(session()->get('Add')); ?></strong>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <?php if(session()->has('delete')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong><?php echo e(session()->get('delete')); ?></strong>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <?php if(session()->has('edit')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong><?php echo e(session()->get('edit')); ?></strong>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <!-- <div class="row"> -->
    <div class="row row-sm">

        

        <?php

            // $date1 = new DateTime($infocompanies->implode('dateofinsertofactive'));
            // $date2 = new DateTime($infocompanies->implode('dateofendofactive'));
            // $interval = $date2->diff($date1);

            $fdate = $infocompanies->implode('dateofinsertofactive');
            $tdate = $infocompanies->implode('dateofendofactive');
            $datetime1 = strtotime($fdate); // convert to timestamps
            $datetime2 = strtotime($tdate); // convert to timestamps
            $days = (int) (($datetime2 - $datetime1) / 86400); // will give the difference in days , 86400 is the timestamp difference of a day

        ?>
        <?php

if ($days < 3) {
// exit program normally
//Redirect::url("/logout")
echo "<script>alert('  .... ضروري جدا  ....لقد بقي من وقت التفعيل  (  $days  )   أيام')</script>";

Auth::logout();
Session::flush();

echo "<script> window.location='login'</script>";

return Redirect::to('login');
return Redirect::to('home');
exit();
}

if ($days < 15) {
// exit program normally
//Redirect::url("/logout")
echo "<script>alert(' لقد بقي من وقت التفعيل (  $days  ) أيام وعلى ذلك الرجاء التجديد قبل وصول البرنامج ل 3 أيام')</script>";




?>



        <div class="alert alert-danger">
            <strong> الرجاء الحذر! </strong> لقد بقي من وقت التفعيل ( <?php echo e($days); ?> ) أيام

            وعلى ذلك الرجاء التجديد قبل وصول البرنامج ل 3 ايام
        </div>


        <?php
}
?>



        
        <div class="col-xl-12">
            <div class="card-body">
                <div class="table-responsive">

                    <div class="panel panel-primary tabs-style-3">
                        <div class="tab-menu-heading">
                            <div class="tabs-menu ">
                                <!-- Tabs -->
                                <ul class="nav panel-tabs">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('IsSoperAndAdmin', Auth::user())): ?>
                                        <li class=""><a href="#tab11" class="active" data-toggle="tab"><i
                                                    class="fa fa-laptop"></i> جميع الحركات </a></li>

                                        <li><a href="#tab12" data-toggle="tab"><i class="fa fa-cube"></i> التحدي الخاص</a>
                                        </li>
                                        <li><a href="#tab13" data-toggle="tab"><i class="fa fa-cogs"></i>معلومات
                                                التحديات </a></li>
                                        <li><a href="#tab14" data-toggle="tab"><i class="fa fa-cogs"></i> معلومات
                                                الزيارات والمواعيد </a></li>
                                        <li><a href="#tab15" data-toggle="tab"><i class="fa fa-tasks"></i> الاشتراك
                                                الخاص </a></li>
                                        <li><a href="#tab16" data-toggle="tab"><i class="fa fa-tasks"></i> معلومات
                                                الاشتراكات </a></li>
                                    <?php endif; ?>



                                    <li><a href="#tab17" data-toggle="tab"><i class="fa fa-tasks"></i> المقبوضات </a>
                                    </li>
                                    <li><a href="#tab18" data-toggle="tab"><i class="fa fa-tasks"></i> تسجيل </a></li>
                                    <li><a href="#tab19" data-toggle="tab"><i class="fa fa-tasks"></i> كشف حساب </a>
                                    </li>


                                </ul>
                            </div>
                        </div>
                        <div class="panel-body tabs-menu-body">
                            <div class="tab-content">

                                <div class="card mg-b-20">
                                </div>
                                <div class="tab-pane active" id="tab11">
                                    لا يوجد استبيان
                                    <div class="row row-sm">
                                        <div class="col-xl-12">
                                            <div class="card">
                                                <div class="card-header pb-0">
                                                    <div class="card-body">
                                                        <div class="table-responsive">



                                                            <table id="example1999" class="table"
                                                                style="max-width:500px">

                                                                <thead>
                                                                    <tr style="text-align: center">




                                                                        <th scope="col">العمليات</th>
                                                                        <th scope="col">المعرف</th>
                                                                        <th scope="col">التاريخ</th>
                                                                        <th scope="col">النوع</th>
                                                                        <th scope="col">الاسم</th>
                                                                        <th scope="col">تاريخ بداية
                                                                            التحدي</th>
                                                                        <th scope="col">عدد أيام التحدي
                                                                        </th>
                                                                        <th scope="col">تاريخ نهاية
                                                                            التحدي</th>
                                                                        <th scope="col">بقي للموعد</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>

                                                                    <?php $i = 0; ?>
                                                                    <?php $__currentLoopData = $historycls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $historycl): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php $i++; ?>

                                                                        <?php if($historycl->type_history == 'tahaadddy'): ?>
                                                                            <tr style="text-align: center">
                                                                                <td></td>

                                                                                <td><?php echo e($i); ?></td>

                                                                                <td> <?php echo e($historycl->created_at); ?></td>

                                                                                <td>
                                                                                    <p style='color:red;'> تحدي </p>
                                                                                </td>
                                                                                <td><?php echo e($historycl->challengesy->tahaddename); ?>


                                                                                <td><?php echo e($historycl->clientschallenges->startdateofcustomerchallenges); ?>

                                                                                </td>
                                                                                <td><?php echo e($historycl->clientschallenges->endtdateofcustomerchallenges); ?>

                                                                                </td>

                                                                                <td>

                                                                                    <?php echo e(date('Y-m-d', strtotime(now()->parse($historycl->clientschallenges->startdateofcustomerchallenges)->addDays($historycl->clientschallenges->endtdateofcustomerchallenges)))); ?>


                                                                                </td>
                                                                                <td>
                                                                                    <?php
                                                                                    
                                                                                    $timeago = date('Y-m-d', strtotime(now()->parse($historycl->clientschallenges->startdateofcustomerchallenges)->addDays($historycl->clientschallenges->endtdateofcustomerchallenges)));
                                                                                    
                                                                                    ?>
                                                                                    <?php
                                                                                    
                                                                                    $date1 = new DateTime('today');
                                                                                    
                                                                                    $date2 = new DateTime($timeago);
                                                                                    
                                                                                    $interval = $date1->diff($date2)->format('%r%a');
                                                                                    
                                                                                    if ($interval < 0) {
                                                                                        echo "<p style='color: rgb(204, 191, 15);'>ذهب موعده </p>";
                                                                                    } elseif ($interval == 0) {
                                                                                        echo "<p style='color: red;'>اليوم موعده </p>";
                                                                                    }
                                                                                    
                                                                                    if ($interval >= 4) {
                                                                                        echo "<p style='color: green;'>بقي    $interval   ايام </p>";
                                                                                    } elseif ($interval < 4 && $interval > 0) {
                                                                                        echo "<p style='color: rgb(144, 37, 148);'>بقي    $interval    ايام </p>";
                                                                                    }
                                                                                    ?>
                                                                                </td>

                                                                            </tr>
                                                                        <?php endif; ?>



                                                                        <?php if($historycl->type_history == 'eshteraak'): ?>
                                                                            <tr style="text-align: center">
                                                                                <td></td>

                                                                                <td><?php echo e($i); ?></td>

                                                                                <td> <?php echo e($historycl->created_at); ?></td>

                                                                                <td>
                                                                                    <p style='color:rgb(11, 35, 102);'>
                                                                                        إشتراك </p>
                                                                                </td>
                                                                                <td><?php echo e($historycl->subscriptions->subscriptionsname); ?>


                                                                                <td><?php echo e($historycl->clientssubscriptions->startdateofcustomersubscriptions); ?>

                                                                                </td>
                                                                                <td><?php echo e($historycl->clientssubscriptions->endtdateofcustomersubscriptions); ?>

                                                                                </td>

                                                                                <td>

                                                                                    <?php echo e(date('Y-m-d', strtotime(now()->parse($historycl->clientssubscriptions->startdateofcustomersubscriptions)->addDays($historycl->clientssubscriptions->endtdateofcustomersubscriptions)))); ?>


                                                                                </td>
                                                                                <td>
                                                                                    <?php
                                                                                    
                                                                                    $timeago = date('Y-m-d', strtotime(now()->parse($historycl->clientssubscriptions->startdateofcustomersubscriptions)->addDays($historycl->clientssubscriptions->endtdateofcustomersubscriptions)));
                                                                                    
                                                                                    ?>
                                                                                    <?php
                                                                                    
                                                                                    $date1 = new DateTime('today');
                                                                                    
                                                                                    $date2 = new DateTime($timeago);
                                                                                    
                                                                                    $interval = $date1->diff($date2)->format('%r%a');
                                                                                    
                                                                                    if ($interval < 0) {
                                                                                        echo "<p style='color: rgb(204, 191, 15);'>ذهب موعده </p>";
                                                                                    } elseif ($interval == 0) {
                                                                                        echo "<p style='color: red;'>اليوم موعده </p>";
                                                                                    }
                                                                                    
                                                                                    if ($interval >= 4) {
                                                                                        echo "<p style='color: green;'>بقي    $interval   ايام </p>";
                                                                                    } elseif ($interval < 4 && $interval > 0) {
                                                                                        echo "<p style='color: rgb(144, 37, 148);'>بقي    $interval    ايام </p>";
                                                                                    }
                                                                                    ?>
                                                                                </td>

                                                                            </tr>
                                                                        <?php endif; ?>




                                                                        <?php if($historycl->type_history == 'zeyara'): ?>
                                                                            <tr style="text-align: center">
                                                                                <td></td>

                                                                                <td><?php echo e($i); ?></td>

                                                                                <td> <?php echo e($historycl->created_at); ?></td>
                                                                                <td>

                                                                                    <p style='color:rgb(11, 102, 90);'>
                                                                                        زيارة
                                                                                    </p>
                                                                                    <?php echo e(@$historycl->challengesy->tahaddename); ?>


                                                                                </td>
                                                                                <td><?php echo e($historycl->visits->note); ?>


                                                                                <td><?php echo e($historycl->visits->dateofvisit); ?>

                                                                                </td>
                                                                                <td><?php echo e($historycl->visits->nextvisit); ?>

                                                                                </td>

                                                                                <td>

                                                                                    <?php echo e(date('Y-m-d', strtotime(now()->parse($historycl->visits->dateofvisit)->addDays($historycl->visits->nextvisit)))); ?>


                                                                                </td>

                                                                                <td>
                                                                                    <?php
                                                                                    
                                                                                    $timeago = date('Y-m-d', strtotime(now()->parse($historycl->visits->dateofvisit)->addDays($historycl->visits->nextvisit)));
                                                                                    
                                                                                    ?>
                                                                                    <?php
                                                                                    
                                                                                    $date1 = new DateTime('today');
                                                                                    
                                                                                    $date2 = new DateTime($timeago);
                                                                                    
                                                                                    $interval = $date1->diff($date2)->format('%r%a');
                                                                                    
                                                                                    if ($interval < 0) {
                                                                                        echo "<p style='color: rgb(204, 191, 15);'>ذهب موعده </p>";
                                                                                    } elseif ($interval == 0) {
                                                                                        echo "<p style='color: red;'>اليوم موعده </p>";
                                                                                    }
                                                                                    
                                                                                    if ($interval >= 4) {
                                                                                        echo "<p style='color: green;'>بقي    $interval   ايام </p>";
                                                                                    } elseif ($interval < 4 && $interval > 0) {
                                                                                        echo "<p style='color: rgb(144, 37, 148);'>بقي    $interval    ايام </p>";
                                                                                    }
                                                                                    ?>
                                                                                </td>
                                                                            </tr>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </tbody>
                                                            </table>


                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane" id="tab12">
                                    <div class="row row-sm">
                                        <div class="col-xl-12">
                                            <div class="card">
                                                <div class="card-header pb-0">
                                                    <div class="card-body">
                                                        <div class="table-responsive">
                                                            <table id="example1" class="table" style="max-width:500px">

                                                                <thead>
                                                                    <tr style="text-align: center">




                                                                        <th scope="col">العمليات</th>


                                                                        <th scope="col">المعرف</th>
                                                                        <th scope="col">اسم التحدي</th>
                                                                        <th scope="col">تاريخ بداية
                                                                            التحدي</th>

                                                                        <th scope="col">عدد أيام التحدي
                                                                        </th>
                                                                        <th scope="col">تاريخ نهاية
                                                                            التحدي</th>


                                                                    </tr>


                                                                </thead>
                                                                <tbody>

                                                                    <?php $i = 0; ?>
                                                                    <?php $__currentLoopData = $clientschallenges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cx): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php $i++; ?>


                                                                        <tr style="text-align: center">
                                                                            <td>



                                                                                <a class="modal-effect btn btn-sm btn-info"
                                                                                    -effect="effect-scale"
                                                                                    data-id="<?php echo e($cx->id); ?>"
                                                                                    data-tahadde_id="<?php echo e($cx->clientschallengesss->tahaddename); ?>"
                                                                                    data-startdateofcustomerchallenges="<?php echo e($cx->startdateofcustomerchallenges); ?>"
                                                                                    data-endtdateofcustomerchallenges="<?php echo e($cx->endtdateofcustomerchallenges); ?>"
                                                                                    data-toggle="modal"
                                                                                    href="#exampleModal2chclient"
                                                                                    title="تعديل"><i
                                                                                        class="las la-pen"></i></a>

                                                                                <a class="modal-effect btn btn-sm btn-danger"
                                                                                    data-effect="effect-scale"
                                                                                    data-id="<?php echo e($cx->id); ?>"
                                                                                    data-tahaddename="<?php echo e($cx->clientschallengesss->tahaddename); ?>"
                                                                                    data-toggle="modal"
                                                                                    href="#modaldemo9chclient"
                                                                                    title="حذف"><i
                                                                                        class="las la-trash"></i></a>

                                                                            </td>

                                                                            <td><?php echo e($i); ?></td>
                                                                            <td><?php echo e($cx->clientschallengesss->tahaddename); ?>

                                                                            </td>

                                                                            <td><?php echo e($cx->startdateofcustomerchallenges); ?>

                                                                            </td>
                                                                            <td><?php echo e($cx->endtdateofcustomerchallenges); ?>

                                                                            </td>


                                                                            <td>

                                                                                <?php echo e(date('Y-m-d', strtotime(now()->parse($cx->startdateofcustomerchallenges)->addDays($cx->endtdateofcustomerchallenges)))); ?>


                                                                            </td>


                                                                        </tr>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </tbody>
                                                            </table>

                                                        </div>


                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane" id="tab13">
                                    <div class="row row-sm">
                                        <div class="col-xl-12">
                                            <div class="card">
                                                <div class="card-header pb-0">
                                                    <div class="card-body">
                                                        <div class="table-responsive">


                                                            <table id="example2" class="table" style="max-width:500px">

                                                                <thead>
                                                                    <tr style="text-align: center">





                                                                        <th scope="col">تحدي</th>

                                                                        <th scope="col">المعرف</th>
                                                                        <th scope="col">اسم التحدي</th>
                                                                        <th scope="col">تاريخ بداية
                                                                            التحدي</th>

                                                                        <th scope="col">عدد أيام التحدي
                                                                        </th>
                                                                        <th scope="col">تاريخ نهاية
                                                                            التحدي</th>


                                                                    </tr>


                                                                </thead>
                                                                <tbody>

                                                                    <?php $i = 0; ?>
                                                                    <?php $__currentLoopData = $challenges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $x): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php $i++; ?>


                                                                        <tr style="text-align: center">
                                                                            <!--       <td>






                                                                                                                                                        <a class="modal-effect btn btn-sm btn-info" -effect="effect-scale"
                                                                                                                                                            data-id="<?php echo e($x->id); ?>"
                                                                                                                                                            data-tahaddename="<?php echo e($x->tahaddename); ?>"
                                                                                                                                                            data-startdate="<?php echo e($x->startdate); ?>"
                                                                                                                                                            data-timeenddate="<?php echo e($x->timeenddate); ?>" data-toggle="modal"
                                                                                                                                                            href="#exampleModal2ch" title="تعديل"><i
                                                                                                                                                                class="las la-pen"></i></a>

                                                                                                                    <a class="modal-effect btn btn-sm btn-danger" data-effect="effect-scale"
                                                                                                                      data-id="<?php echo e($x->id); ?>" data-tahaddename="<?php echo e($x->tahaddename); ?>" data-toggle="modal"
                                                                                                                      href="#modaldemo9ch" title="حذssف"><i class="las la-trash"></i></a>

                                                                                                                                                    </td>   -->





                                                                            <?php
//if( $x->id != $cx->tahadde_id)




  //$kkkkkk = DB::table('clientschallenges')->where('tahadde_id', $x->id)->get()
          //dd($kkkkkk->tahadde_id);


//$kkkkkk = DB::table('clientschallenges')->where('tahadde_id', $x->id)->first();



$kkkkkk = DB::table('clientschallenges')
->where('tahadde_id', $x->id)
->where('clients_id', $clients->id)
->first();


if ($x->id !=  @$kkkkkk->tahadde_id)

{

                ?>





                                                                            <td> <a class="btn btn-success btn-block"
                                                                                    data-effect="effect-scale"
                                                                                    data-toggle="modal"
                                                                                    data-id="<?php echo e($x->id); ?>"
                                                                                    data-tahaddename="<?php echo e($x->tahaddename); ?>"
                                                                                    data-timeenddate="<?php echo e($x->timeenddate); ?>"
                                                                                    href="#modaldemo8chcl">تحدي</a>

                                                                                <?php
    }
     else{

    ?>

                                                                            <td> <a class="modal-effect btn btn-outline-primary btn-block"
                                                                                    data-effect="effect-scale"
                                                                                    data-toggle="modal"
                                                                                    data-id="<?php echo e($x->id); ?>"
                                                                                    data-tahaddename="<?php echo e($x->tahaddename); ?>"
                                                                                    data-timeenddate="<?php echo e($x->timeenddate); ?>"
                                                                                    href="#">مشترك</a>




                                                                                <?php
            }
    ?>




                                                                            </td>

                                                                            <td><?php echo e($i); ?></td>
                                                                            <td><?php echo e($x->tahaddename); ?></td>
                                                                            <td><?php echo e($x->startdate); ?></td>
                                                                            <td><?php echo e($x->timeenddate); ?></td>


                                                                            <td>

                                                                                <?php echo e(date('Y-m-d', strtotime(now()->parse($x->startdate)->addDays($x->timeenddate)))); ?>


                                                                            </td>


                                                                        </tr>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </tbody>
                                                            </table>



                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane" id="tab14">

                                    <div class="row row-sm">
                                        <div class="col-xl-12">
                                            <div class="card">
                                                <div class="card-header pb-0">
                                                    <div class="card-body">
                                                        <div class="table-responsive">


                                                            <a class="modal-effect btn btn-outline-primary btn-block"
                                                                data-effect="effect-scale" data-toggle="modal"
                                                                href="#modaldemo8visit">إضافة زيارة وموعد</a>



                                                            <table id="example3" class="table" style="max-width:500px">

                                                                <thead>
                                                                    <tr style="text-align: center">




                                                                        <th scope="col">العمليات</th>


                                                                        <th scope="col">المعرف</th>
                                                                        <th scope="col">الملاحظة</th>
                                                                        <th scope="col">تاريخ الزيارة
                                                                        </th>
                                                                        <th scope="col">عدد الايام</th>

                                                                        <th scope="col">تاريخ الزيارة
                                                                            القادمة</th>


                                                                    </tr>


                                                                </thead>
                                                                <tbody>

                                                                    <?php $i = 0; ?>
                                                                    <?php $__currentLoopData = $visits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vis): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php $i++; ?>


                                                                        <tr style="text-align: center">
                                                                            <td>






                                                                                <a class="modal-effect btn btn-sm btn-info"
                                                                                    -effect="effect-scale"
                                                                                    data-id="<?php echo e($vis->id); ?>"
                                                                                    data-note="<?php echo e($vis->note); ?>"
                                                                                    data-dateofvisit="<?php echo e($vis->dateofvisit); ?>"
                                                                                    data-nextvisit="<?php echo e($vis->nextvisit); ?>"
                                                                                    data-toggle="modal"
                                                                                    href="#exampleModal2chclientvisit"
                                                                                    title="تعديل"><i
                                                                                        class="las la-pen"></i></a>

                                                                                <a class="modal-effect btn btn-sm btn-danger"
                                                                                    data-effect="effect-scale"
                                                                                    data-id="<?php echo e($vis->id); ?>"
                                                                                    data-note="<?php echo e($vis->note); ?>"
                                                                                    data-toggle="modal"
                                                                                    href="#modaldemo9chclientvisit"
                                                                                    title="حذف"><i
                                                                                        class="las la-trash"></i></a>

                                                                            </td>

                                                                            <td><?php echo e($i); ?></td>
                                                                            <td><?php echo e($vis->note); ?></td>

                                                                            <td><?php echo e($vis->dateofvisit); ?></td>

                                                                            <td><?php echo e($vis->nextvisit); ?></td>
                                                                            <td>

                                                                                <?php echo e(date('Y-m-d', strtotime(now()->parse($vis->dateofvisit)->addDays($vis->nextvisit)))); ?>


                                                                            </td>


                                                                        </tr>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </tbody>
                                                            </table>


                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane" id="tab15">
                                    <div class="row row-sm">
                                        <div class="col-xl-12">
                                            <div class="card">
                                                <div class="card-header pb-0">
                                                    <div class="card-body">
                                                        <div class="table-responsive">




                                                            <table id="example4" class="table" style="max-width:500px">

                                                                <thead>
                                                                    <tr style="text-align: center">




                                                                        <th scope="col">العمليات</th>


                                                                        <th scope="col">المعرف</th>
                                                                        <th scope="col">اسم الاشتراك
                                                                        </th>
                                                                        <th scope="col">تاريخ بداية
                                                                            الاشتراك</th>

                                                                        <th scope="col">عدد أيام الاشتراك
                                                                        </th>
                                                                        <th scope="col">تاريخ نهاية
                                                                            الاشتراك</th>


                                                                    </tr>


                                                                </thead>
                                                                <tbody>

                                                                    <?php $i = 0; ?>
                                                                    <?php $__currentLoopData = $clientssubscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cxs): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php $i++; ?>


                                                                        <tr style="text-align: center">
                                                                            <td>






                                                                                <a class="modal-effect btn btn-sm btn-info"
                                                                                    -effect="effect-scale"
                                                                                    data-id="<?php echo e($cxs->id); ?>"
                                                                                    data-subscriptions_id="<?php echo e($cxs->clientschallengesssub->subscriptionsname); ?>"
                                                                                    data-startdateofcustomersubscriptions="<?php echo e($cxs->startdateofcustomersubscriptions); ?>"
                                                                                    data-endtdateofcustomersubscriptions="<?php echo e($cxs->endtdateofcustomersubscriptions); ?>"
                                                                                    data-toggle="modal"
                                                                                    href="#exampleModal2chclientsubscriptions"
                                                                                    title="تعديل"><i
                                                                                        class="las la-pen"></i></a>

                                                                                <a class="modal-effect btn btn-sm btn-danger"
                                                                                    data-effect="effect-scale"
                                                                                    data-id="<?php echo e($cxs->id); ?>"
                                                                                    data-subscriptions_id="<?php echo e($cxs->clientschallengesssub->subscriptionsname); ?>"
                                                                                    data-toggle="modal"
                                                                                    href="#modaldemo9chclientsubscriptions"
                                                                                    title="حذف"><i
                                                                                        class="las la-trash"></i></a>

                                                                            </td>

                                                                            <td><?php echo e($i); ?></td>
                                                                            <td><?php echo e($cxs->clientschallengesssub->subscriptionsname); ?>

                                                                            </td>

                                                                            <td><?php echo e($cxs->startdateofcustomersubscriptions); ?>

                                                                            </td>
                                                                            <td><?php echo e($cxs->endtdateofcustomersubscriptions); ?>

                                                                            </td>


                                                                            <td>

                                                                                <?php echo e(date('Y-m-d', strtotime(now()->parse($cxs->startdateofcustomersubscriptions)->addDays($cxs->endtdateofcustomersubscriptions)))); ?>


                                                                            </td>


                                                                        </tr>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </tbody>
                                                            </table>


                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane" id="tab16">
                                    <div class="row row-sm">
                                        <div class="col-xl-12">
                                            <div class="card">
                                                <div class="card-header pb-0">
                                                    <div class="card-body">
                                                        <div class="table-responsive">




                                                            <table id="example5" class="table" style="max-width:500px">

                                                                <thead>
                                                                    <tr style="text-align: center">





                                                                        <th scope="col">الاشتراك</th>

                                                                        <th scope="col">المعرف</th>
                                                                        <th scope="col">اسم الاشتراك
                                                                        </th>
                                                                        <th scope="col">تاريخ بداية
                                                                            الاشتراك</th>

                                                                        <th scope="col">عدد أيام الاشتراك
                                                                        </th>
                                                                        <th scope="col">تاريخ نهاية
                                                                            الاشتراك</th>


                                                                    </tr>


                                                                </thead>
                                                                <tbody>

                                                                    <?php $i = 0; ?>
                                                                    <?php $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $xs): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php $i++; ?>


                                                                        <tr style="text-align: center">
                                                                            <!--           <td>






                                                                                                                                                        <a class="modal-effect btn btn-sm btn-info" -effect="effect-scale"
                                                                                                                                                            data-id="<?php echo e($xs->id); ?>"
                                                                                                                                                            data-subscriptionsname="<?php echo e($xs->subscriptionsname); ?>"
                                                                                                                                                            data-startdatesubscriptions="<?php echo e($xs->startdatesubscriptions); ?>"
                                                                                                                                                            data-timeenddatesubscriptions="<?php echo e($xs->timeenddatesubscriptions); ?>" data-toggle="modal"
                                                                                                                                                            href="#exampleModal2chsubscriptions" title="تعديل"><i
                                                                                                                                                                class="las la-pen"></i></a>

                                                                                                                    <a class="modal-effect btn btn-sm btn-danger" data-effect="effect-scale"
                                                                                                                      data-id="<?php echo e($xs->id); ?>" data-tahaddename="<?php echo e($xs->subscriptionsname); ?>" data-toggle="modal"
                                                                                                                      href="#modaldemo9ch" title="حذssف"><i class="las la-trash"></i></a>

                                                                                                                                                    </td>      -->





                                                                            <?php
//if( $x->id != $cx->tahadde_id)







if ($xs->id !=  @$cxs->subscriptions_id)

{

                ?>





                                                                            <td> <a class="btn btn-success btn-block"
                                                                                    data-effect="effect-scale"
                                                                                    data-toggle="modal"
                                                                                    data-id="<?php echo e($xs->id); ?>"
                                                                                    data-subscriptionsname="<?php echo e($xs->subscriptionsname); ?>"
                                                                                    data-timeenddatesubscriptions="<?php echo e($xs->timeenddatesubscriptions); ?>"
                                                                                    href="#modaldemo8chclsubscriptions">إشتراك</a>

                                                                                <?php
    }
     else{

    ?>

                                                                            <td> <a class="modal-effect btn btn-outline-primary btn-block"
                                                                                    data-effect="effect-scale"
                                                                                    data-toggle="modal"
                                                                                    data-id="<?php echo e($xs->id); ?>"
                                                                                    data-subscriptionsname="<?php echo e($xs->subscriptionsname); ?>"
                                                                                    data-timeenddatesubscriptions="<?php echo e($xs->timeenddatesubscriptions); ?>"
                                                                                    href="#">مشترك</a>




                                                                                <?php
            }
    ?>




                                                                            </td>

                                                                            <td><?php echo e($i); ?></td>
                                                                            <td><?php echo e($xs->subscriptionsname); ?></td>
                                                                            <td><?php echo e($xs->startdatesubscriptions); ?></td>
                                                                            <td><?php echo e($xs->timeenddatesubscriptions); ?></td>


                                                                            <td>

                                                                                <?php echo e(date('Y-m-d', strtotime(now()->parse($xs->startdatesubscriptions)->addDays($xs->timeenddatesubscriptions)))); ?>


                                                                            </td>


                                                                        </tr>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </tbody>
                                                            </table>




                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane" id="tab17">


                                    <form action="<?php echo e(route('moneys.store')); ?>" method="post"
                                        enctype="multipart/form-data" autocomplete="off">
                                        <?php echo e(csrf_field()); ?>

                                        

                                        <div class="row">


                                            <div class="col">
                                                <label>اسم المستخدم</label>
                                                <input class="form-control fc-datepicker" value="<?php echo e($clients->name); ?>"
                                                    type="text" readonly="readonly">
                                            </div>




                                            <div class="col">

                                                <input type="hidden" class="form-control" id="clients_id"
                                                    name="clients_id" value="<?php echo e($clients->id); ?>">
                                                <label>تاريخ الحالة</label>
                                                <input type="date" class="form-control" id="dateofmoney"
                                                    name="dateofmoney" placeholder="YYYY-MM-DD" type="text"
                                                    value="<?php echo e(date('Y-m-d')); ?>" required>




                                            </div>




                                        </div>

                                        
                                        <br>
                                        <div class="row">
                                            <div class="col">
                                                <label for="inputName" class="control-label">المبلغ المقبوض</label>

                                                <input class="form-control fc-datepicker" name="kabdmoney" type="number"
                                                    id="kabdmoney" required>

                                                <input class="form-control fc-datepicker" name="sarf" type="hidden"
                                                    id="sarf" value="0" required>
                                            </div>

                                            <?php
                                            
                                            $sumsarf = DB::table('moneys')->where('clients_id', $clients->id)->get()->sum('sarf');
                                            
                                            $sumkabdmoney = DB::table('moneys')->where('clients_id', $clients->id)->get()->sum('kabdmoney');
                                            
                                            // $sumkabdmoney =DB::table("money")->get()->sum("kabdmoney");
                                            
                                            $sumsom = $sumsarf - $sumkabdmoney;
                                            
                                            ?>
                                            <input type="hidden" class="form-control" id="almajmoooa" name="almajmoooa"
                                                value="<?php echo e($sumsom); ?>" readonly="readonly">


                                            <div class="col">






                                                <label for="inputName" class="control-label">الرصيد</label>
                                                <input type="text" class="form-control" id="sum" name="sum"
                                                    value="<?php echo e($sumsom); ?>" readonly="readonly">
                                                <?php
                                                if ($sumsom < '0') {
                                                    echo "<p style='color:  green;'>  له  $sumsom    </p>";
                                                } elseif ($sumsom > '-1') {
                                                    echo "<p style='color:red;'> عليه $sumsom    </p>";
                                                }
                                                ?>

                                            </div>
                                        </div>




                                        <br>

                                        
                                        <div class="row">
                                            <div class="col">
                                                <label for="exampleTextarea">ملاحظات</label>
                                                <textarea class="form-control" id="note" name="note" rows="3" required></textarea>
                                            </div>
                                        </div><br>




                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary">حفظ البيانات</button>

                                            <button type="reset" onclick="ClearFields()" class="btn btn-secondary"
                                                data-dismiss="modal">اغلاق</button>
                                        </div>


                                    </form>
                                </div>



                                <div class="tab-pane" id="tab18">

                                    <form action="<?php echo e(route('moneys.store')); ?>" method="post"
                                        enctype="multipart/form-data" autocomplete="off">
                                        <?php echo e(csrf_field()); ?>

                                        

                                        <div class="row">


                                            <div class="col">
                                                <label>اسم المستخدم</label>
                                                <input class="form-control fc-datepicker" value="<?php echo e($clients->name); ?>"
                                                    type="text" readonly="readonly">
                                            </div>




                                            <div class="col">

                                                <input type="hidden" class="form-control" id="clients_id"
                                                    name="clients_id" value="<?php echo e($clients->id); ?>">
                                                <label>تاريخ الحالة</label>
                                                <input type="date" class="form-control" id="dateofmoney"
                                                    name="dateofmoney" placeholder="YYYY-MM-DD" type="text"
                                                    value="<?php echo e(date('Y-m-d')); ?>" required>




                                            </div>




                                        </div>

                                        
                                        <br>
                                        <div class="row">
                                            <div class="col">
                                                <label for="inputName" class="control-label">تسجيل المبلغ</label>

                                                <input class="form-control fc-datepicker" name="kabdmoneyy"
                                                    type="hidden" id="kabdmoneyy" value="0" required>

                                                <input class="form-control fc-datepicker" name="sarfy" type="number"
                                                    id="sarfy" required>
                                            </div>


                                            <?php
                                            
                                            $sumsarfy = DB::table('moneys')->where('clients_id', $clients->id)->get()->sum('sarf');
                                            
                                            $sumkabdmoneyy = DB::table('moneys')->where('clients_id', $clients->id)->get()->sum('kabdmoney');
                                            
                                            // $sumkabdmoney =DB::table("money")->get()->sum("kabdmoney");
                                            
                                            $sumsomy = $sumsarfy - $sumkabdmoneyy;
                                            
                                            ?>

                                            <input type="hidden" class="form-control" id="almajmoooay"
                                                name="almajmoooay" value="<?php echo e($sumsomy); ?>" readonly="readonly">


                                            <div class="col">
                                                <label for="inputName" class="control-label">الرصيد</label>


                                                <input type="text" class="form-control" value="<?php echo e($sumsomy); ?>"
                                                    id="sumy" name="sumy" readonly="readonly">
                                                <?php
                                                if ($sumsomy < '0') {
                                                    echo "<p style='color:  green;'>  له  $sumsomy    </p>";
                                                } elseif ($sumsomy > '-1') {
                                                    echo "<p style='color:red;'> عليه $sumsomy    </p>";
                                                }
                                                ?>
                                            </div>

                                        </div>




                                        <br>

                                        
                                        <div class="row">
                                            <div class="col">
                                                <label for="exampleTextarea">ملاحظات</label>
                                                <textarea class="form-control" id="note" name="note" rows="3" required></textarea>
                                            </div>
                                        </div><br>



                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary">حفظ البيانات</button>

                                            <button type="reset" onclick="ClearFields()" class="btn btn-secondary"
                                                data-dismiss="modal">اغلاق</button>
                                        </div>












                                    </form>
                                </div>
                                <div class="tab-pane" id="tab19">
                                    <div class="row row-sm">
                                        <div class="col-xl-12">
                                            <div class="card">
                                                <div class="card-header pb-0">
                                                    <div class="card-body">
                                                        <div class="table-responsive">



                                                            <h2>
                                                                حساب :<?php echo e($clients->name); ?>

                                                            </h2>

                                                            <h2 class="text-danger">

                                                                <?php
                                                                
                                                                $sumsarfff = DB::table('moneys')->where('clients_id', $clients->id)->get()->sum('sarf');
                                                                
                                                                $sumkabdmoneyff = DB::table('moneys')->where('clients_id', $clients->id)->get()->sum('kabdmoney');
                                                                
                                                                // $sumkabdmoney =DB::table("money")->get()->sum("kabdmoney");
                                                                
                                                                $sumsomhomeff = $sumsarfff - $sumkabdmoneyff;
                                                                
                                                                if ($sumsomhomeff < '0') {
                                                                    echo "<p style='color:  green;'>  له  $sumsomhomeff    </p>";
                                                                } elseif ($sumsomhomeff > '-1') {
                                                                    echo "<p style='color:red;'> عليه $sumsomhomeff    </p>";
                                                                }
                                                                
                                                                ?>





                                                            </h2>


                                                            <table id="example6" class="table" style="max-width:500px">

                                                                <!--   <a class="modal-effect btn btn-outline-primary btn-block" data-effect="effect-scale" data-toggle="modal" href="#modaldemo8">إضافة سؤال</a> -->
                                                                <thead>
                                                                    <tr style="text-align: center">
                                                                        <th scope="col">العمليات</th>
                                                                        <th scope="col">المعرف</th>
                                                                        <th scope="col">التاريخ</th>

                                                                        <th scope="col">المبلغ المدفوع</th>
                                                                        <th scope="col">المبلغ المسجل</th>

                                                                        <th scope="col">الملاحظة</th>
                                                                        <th scope="col">الرصيد</th>
                                                                        <th scope="col">الحالة</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>

                                                                    <?php
                                                                    
                                                                    $i = 0;
                                                                    $kabdmoney1 = 0;
                                                                    $sarf1 = 0;
                                                                    
                                                                    ?>
                                                                    <?php $__currentLoopData = $moneys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $xm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php $i++; ?>






                                                                        <tr style="text-align: center">
                                                                            <td>
                                                                                <a class="modal-effect btn btn-sm btn-info"
                                                                                    -effect="effect-scale"
                                                                                    data-id="<?php echo e($xm->id); ?>"
                                                                                    data-dateofmoney="<?php echo e($xm->dateofmoney); ?>"
                                                                                    data-kabdmoney="<?php echo e($xm->kabdmoney); ?>"
                                                                                    data-sarf="<?php echo e($xm->sarf); ?>"
                                                                                    data-note="<?php echo e($xm->note); ?>"
                                                                                    data-toggle="modal"
                                                                                    href="#exampleModal2chclientmoney"
                                                                                    title="تعديل"><i
                                                                                        class="las la-pen"></i></a>

                                                                                <a class="modal-effect btn btn-sm btn-danger"
                                                                                    data-effect="effect-scale"
                                                                                    data-id="<?php echo e($xm->id); ?>"
                                                                                    data-toggle="modal"
                                                                                    href="#modaldemo9chclientmoney"
                                                                                    title="حذف"><i
                                                                                        class="las la-trash"></i></a>

                                                                            </td>
                                                                            </td>
                                                                            <td><?php echo e($i); ?></td>
                                                                            <td><?php echo e($xm->dateofmoney); ?></td>


                                                                            <td><?php echo e($xm->kabdmoney); ?></td>
                                                                            <td><?php echo e($xm->sarf); ?></td>
                                                                            <td><?php echo e($xm->note); ?></td>

                                                                            <td>



                                                                                <?php
                                                                                
                                                                                $sarf1 = $sarf1 + $xm->sarf;
                                                                                
                                                                                $kabdmoney1 = $kabdmoney1 + $xm->kabdmoney;
                                                                                
                                                                                $tntt = $sarf1 - $kabdmoney1;
                                                                                
                                                                                //echo $tntt;
                                                                                
                                                                                if ($tntt < '0') {
                                                                                    echo "<p style='color: green;'>     $tntt    </p>";
                                                                                } elseif ($tntt > '-1') {
                                                                                    echo "<p style='color:red; '>     $tntt    </p>";
                                                                                }
                                                                                
                                                                                ?>



                                                                            </td>

                                                                            <td>

                                                                                <?php
                                                                                if ($tntt < '0') {
                                                                                    echo "<p style='color: green;'>    له   </p>";
                                                                                } elseif ($tntt > '-1') {
                                                                                    echo "<p style='color:red; '>    عليه   </p>";
                                                                                }
                                                                                
                                                                                ?>
                                                                            </td>



                                                                        </tr>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                                                </tbody>


                                                            </table>


                                                            <hr>
                                                            <div style="display:inline-block;float:left;width:100px;">
                                                                <h4> المجموع </h4>
                                                                <?php
                                                                
                                                                $sumsarfff = DB::table('moneys')->where('clients_id', $clients->id)->get()->sum('sarf');
                                                                
                                                                $sumkabdmoneyff = DB::table('moneys')->where('clients_id', $clients->id)->get()->sum('kabdmoney');
                                                                
                                                                // $sumkabdmoney =DB::table("money")->get()->sum("kabdmoney");
                                                                
                                                                $sumsomhomeff = $sumsarfff - $sumkabdmoneyff;
                                                                
                                                                if ($sumsomhomeff < '0') {
                                                                    echo "<p style='color:  green;'>  له  $sumsomhomeff    </p>";
                                                                } elseif ($sumsomhomeff > '-1') {
                                                                    echo "<p style='color:red;'> عليه $sumsomhomeff    </p>";
                                                                }
                                                                
                                                                ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>





                </div>



            </div>

            <!-- Basic modal اضافة زيارة وموعد- -->
            <div class="modal" id="modaldemo8visit">
                <div class="modal-dialog" role="document">
                    <div class="modal-content modal-content-demo">
                        <div class="modal-header">

                            <h6 class="modal-title"> إضافة زيارة و موعد</h6><button aria-label="Close" class="close"
                                data-dismiss="modal" type="button"><span aria-hidden="true">&times;</span></button>


                        </div>

                        <div class="modal-body">

                            <form action="<?php echo e(route('visits.store')); ?>" method="post">
                                <?php echo e(csrf_field()); ?>

                                <div class="form-group">

                                    <input class="form-control" name="clients_id" type="hidden" id="clients_id"
                                        value="<?php echo e($clients->id); ?>" readonly>


                                    <label for="exampleInputEmail1">ملاحظة</label>
                                    <!--  <input type="text"  class="form-control" id="note" name="note">-->


                                    <textarea cols="45%" rows="6" id="note" name="note" required></textarea>

                                    <?php
                                    if (isset($cx->id))
                        {

                            $maxId = DB::table('visits')->max('id');
                                    $maxmaxIddd=$maxId+"1";




                        ?>
                                    <br>
                                    <label class="my-1 mr-2" for="inlineFormCustomSelectPref">التحديات</label>
                                    <select name="challenges_id" id="challenges_id" class="form-control">
                                        <option value="" selected disabled> --حدد التحدي--</option>
                                        <?php $__currentLoopData = $clientschallenges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $clientschallengesssss): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($clientschallengesssss->tahadde_id); ?>">
                                                <?php echo e($clientschallengesssss->clientschallengesss->tahaddename); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>


                                    <input type="hidden" name="visit_id" id="visit_id" value="<?php echo e($maxmaxIddd); ?>">



                                    <?php
                        }
                        else {
                 echo "<br>";
                            echo "<br>";
                            echo "<font color='red'>"."يرجى الانتباه لا يوجد تحدي"."</font>";
                            echo "<br>";
                            echo "<br>";
                        }
                        ?>
                                    <label for="exampleInputEmail1">تاريخ الزيارة </label>
                                    <input type="date" value="<?php echo date('Y-m-d'); ?>" class="form-control"
                                        id="dateofvisit" name="dateofvisit">


                                    <label for="exampleInputEmail1">الايام </label>
                                    <input type="number" value="<?php echo date('Y-m-d'); ?>" class="form-control"
                                        id="nextvisit" name="nextvisit" required>

                                    <label for="exampleInputEmail1">تاريخ الزيارة القادمة</label>

                                    <input class="form-control" type="text" id="fdatevisit" value="" readonly>

                                    <input class="form-control" type="text" id="fdatevisitt" value="" readonly>

                                    <div class="modal-footer">

                                        <button type="submit" class="btn btn-success">تاكيد</button>
                                        <button type="reset" onclick="ClearFields()" class="btn btn-secondary"
                                            data-dismiss="modal">اغلاق</button>

                                    </div>
                            </form>

                        </div>

                    </div>

                </div>
            </div>
            <!-- End Basic modal  اضافة زيارة وموعد-->





        </div>
    </div>















    </div>

    <!-- Basic modal إضافة تحدي للزبون-->
    <div class="modal" id="modaldemo8chcl">
        <div class="modal-dialog" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h6 class="modal-title">إضافة تحدي للزبون </h6><button aria-label="Close" class="close"
                        data-dismiss="modal" type="button"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('clientschallenges.store')); ?>" method="post">
                        <?php echo e(csrf_field()); ?>






                        <div class="form-group">




                            <input type="hidden" class="form-control" id="clients_id" name="clients_id"
                                value="<?php echo e($clients->id); ?>" required>


                            <input type="hidden" class="form-control" id="id" name="id" required>



                            <label for="exampleInputEmail1">اسم التحدي</label>
                            <input type="text" class="form-control" id="tahaddename" name="tahaddename"
                                readonly="readonly" required>








                            <label for="exampleInputEmail1">تاريخ بداية التحدي</label>
                            <input type="date" class="form-control" name="startdate" id="startdate"
                                value="<?php echo date('Y-m-d'); ?>" required>



                            <label for="exampleInputEmail1"> عدد ايام التحدي</label>
                            <input type="number" class="form-control" id="timeenddate" name="timeenddate" required>


                            <label for="exampleInputEmail1">تاريخ نهاية التحدي</label>

                            <input class="form-control" type="text" id="fdate" value="" readonly>
                            <input class="form-control" type="text" id="fdatee" value="" readonly>


                        </div>




                        <div class="modal-footer">

                            <button type="submit" class="btn btn-success">تاكيد</button>
                            <button type="reset" onclick="ClearFields()" class="btn btn-secondary"
                                data-dismiss="modal">اغلاق</button>


                        </div>
                    </form>

                </div>

            </div>

        </div>
        <!-- End Basic modal -->







    </div>







    <!-- Basic modal إضافة اشتراك للزبون-->
    <div class="modal" id="modaldemo8chclsubscriptions">
        <div class="modal-dialog" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h6 class="modal-title">إضافة إشتراك للزبون </h6><button aria-label="Close" class="close"
                        data-dismiss="modal" type="button"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('clientssubscriptions.store')); ?>" method="post">
                        <?php echo e(csrf_field()); ?>






                        <div class="form-group">




                            <input type="hidden" class="form-control" id="clients_id" name="clients_id"
                                value="<?php echo e($clients->id); ?>" required>


                            <input type="text" class="form-control" id="id" name="id" required>



                            <label for="exampleInputEmail1">اسم الاشتراك</label>
                            <input type="text" class="form-control" id="subscriptionsname" readonly="readonly"
                                name="subscriptionsname" required>








                            <label for="exampleInputEmail1">تاريخ بداية الاشتراك</label>
                            <input type="date" class="form-control" name="startdatesubscriptions"
                                id="startdatesubscriptions" value="<?php echo date('Y-m-d'); ?>" required>



                            <label for="exampleInputEmail1"> عدد ايام الاشتراك</label>
                            <input type="number" class="form-control" id="timeenddatesubscriptions"
                                name="timeenddatesubscriptions" required>


                            <label for="exampleInputEmail1">تاريخ نهاية الاشتراك</label>

                            <input class="form-control" type="text" id="fdatesubscriptions" value="" readonly>
                            <input class="form-control" type="text" id="fdatesubscriptionss" value="" readonly>


                        </div>




                        <div class="modal-footer">

                            <button type="submit" class="btn btn-success">تاكيد</button>
                            <button type="reset" onclick="ClearFields()" class="btn btn-secondary"
                                data-dismiss="modal">اغلاق</button>


                        </div>
                    </form>

                </div>

            </div>

        </div>
        <!-- End Basic modal -->







    </div>
    </div>


    <!-- edit تعديل الاشتراكات  -->
    <div class="modal fade" id="exampleModal2chsubscriptions" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModal2chsubscriptions">تعديل الاشتراكات</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action='../subscriptions/update' method="post" autocomplete="off">
                    <?php echo e(method_field('patch')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">

                        <div class="form-group">
                            <label for="title">اسم الاشتراك :</label>

                            <input type="hidden" class="form-control" name="id" id="id">

                            <input type="text" class="form-control" name="subscriptionsname" id="subscriptionsname">


                            <label for="title">تاريخ بداية الاشتراك :</label>

                            <input type="date" class="form-control" name="startdatesubscriptions"
                                id="startdatesubscriptions">


                            <label for="title">وقت الاشتراك :</label>

                            <input type="number" class="form-control" name="timeenddatesubscriptions"
                                id="timeenddatesubscriptions">




                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    </div>
                </form>
            </div>
        </div>

    </div>



    <!-- edit تعديل التحديات  -->
    <div class="modal fade" id="exampleModal2ch" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModal2ch">تعديل تحدي</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action='../challenges/update' method="post" autocomplete="off">
                    <?php echo e(method_field('patch')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">

                        <div class="form-group">
                            <label for="title">اسم التحدي :</label>

                            <input type="hidden" class="form-control" name="id" id="id">

                            <input type="text" class="form-control" name="tahaddename" id="tahaddename">


                            <label for="title">تاريخ بداية التحدي :</label>

                            <input type="date" class="form-control" name="startdate" id="startdate">


                            <label for="title">وقت التحدي :</label>

                            <input type="number" class="form-control" name="timeenddate" id="timeenddate">




                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    </div>
                </form>
            </div>
        </div>

    </div>




    <!-- delete حذف الاشتراك الخاص -->
    <div class="modal fade" id="modaldemo9chclientsubscriptions" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حذف اشتراك خاص</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>


                <form action="../clientssubscriptions/destroy" method="post">
                    <?php echo e(method_field('delete')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">
                        <p>هل انت متاكد من عملية الحذف ؟</p><br>
                        <input type="hidden" name="id" id="id" value="">
                        <input type="hidden" name="clients_id" id="clients_id" value="<?php echo e($clients->id); ?>">



                        <input class="form-control" name="subscriptions_id" id="subscriptions_id" type="text"
                            readonly>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">الغاء</button>
                        <button type="submit" class="btn btn-danger">تاكيد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    </div>





    <!-- delete  حذف المال الخاص بالزبون-->
    <div class="modal fade" id="modaldemo9chclientmoney" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حذف المال </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>


                <form action="../moneys/destroy" method="post">
                    <?php echo e(method_field('delete')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">
                        <p>هل انت متاكد من عملية الحذف ؟</p><br>
                        <input type="text" name="id" id="id" value="">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">الغاء</button>
                        <button type="submit" class="btn btn-danger">تاكيد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    </div>



    <!-- edit تعديل المال الخاص بالزبون -->
    <div class="modal fade" id="exampleModal2chclientmoney" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModal2chclientmoney">تعديل معلومات المال الخاص بالزبون</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>




                <form action='../moneys/update' method="post" autocomplete="off">
                    <?php echo e(method_field('patch')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">

                        <div class="form-group">
                            <div class="alert alert-danger" role="alert">

                                في خانة القبض او التسجيل الرجاء عدم التغيير على الرقم صفر (0)

                            </div>


                            <label for="title">تاريخ االمال :</label>
                            <input type="date" class="form-control" name="dateofmoney" id="dateofmoney">




                            <input type="hidden" class="form-control" name="id" id="id">


                            <label for="title">القبض</label>


                            <input type="number" class="form-control" name="kabdmoney" id="kabdmoney">




                            <label for="title">تسجيل:</label>
                            <input type="number" class="form-control" name="sarf" id="sarf">



                            <label for="title">الملاحظة:</label>
                            <textarea class="form-control" id="note" name="note" rows="3" required></textarea>




                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    </div>


                </form>


            </div>

        </div>

    </div>


    </div>













    <!-- delete  حذف التحدي الخاص بالزبون-->
    <div class="modal fade" id="modaldemo9chclient" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حذف تحدي</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>


                <form action="../clientschallenges/destroy" method="post">
                    <?php echo e(method_field('delete')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">
                        <p>هل انت متاكد من عملية الحذف ؟</p><br>
                        <input type="hidden" name="id" id="id" value="">
                        <input class="form-control" name="tahaddename" id="tahaddename" type="text" readonly>
                        <input type="hidden" name="clients_id" id="clients_id" value="<?php echo e($clients->id); ?>">

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">الغاء</button>
                        <button type="submit" class="btn btn-danger">تاكيد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    </div>



    <!-- edit تعديل التحدي الخاص بالزبون -->
    <div class="modal fade" id="exampleModal2chclient" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModal2chclient">تعديل معلومات التحدي الخاص بالزبون</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action='../clientschallenges/update' method="post" autocomplete="off">
                    <?php echo e(method_field('patch')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">

                        <div class="form-group">
                            <label for="title">الاسم : <?php echo e($clients->name); ?></label>
                            <input type="hidden" class="form-control" name="id" id="id">
                            <input type="hidden" class="form-control" name="estps_id" id="estps_id">
                            <br>



                            <label for="title">اسم التحدي :</label>
                            <input type="text" class="form-control" name="tahadde_id" id="tahadde_id"
                                readonly="readonly">


                            <label for="title">تاريخ بداية التحدي :</label>
                            <input type="date" class="form-control" name="startdateofcustomerchallenges"
                                id="startdateofcustomerchallenges">


                            <label for="title">عدد الايام :</label>
                            <input type="number" class="form-control" name="endtdateofcustomerchallenges"
                                id="endtdateofcustomerchallenges">





                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    </div>


                </form>


            </div>

        </div>

    </div>


    </div>


    <!-- edit تعديل الاشتراك الخاص بالزبون -->
    <div class="modal fade" id="exampleModal2chclientsubscriptions" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModal2chclientsubscriptions">تعديل معلومات الاشتراك الخاص بالزبون
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action='../clientssubscriptions/update' method="post" autocomplete="off">
                    <?php echo e(method_field('patch')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">

                        <div class="form-group">
                            <label for="title">الاسم : <?php echo e($clients->name); ?></label>
                            <input type="hidden" class="form-control" name="id" id="id">
                            <input type="hidden" class="form-control" name="clients_id" id="clients_id">
                            <br>



                            <label for="title">اسم التحدي :</label>
                            <input type="text" class="form-control" name="subscriptions_id" id="subscriptions_id"
                                readonly="readonly">


                            <label for="title">تاريخ بداية التحدي :</label>
                            <input type="date" class="form-control" name="startdateofcustomersubscriptions"
                                id="startdateofcustomersubscriptions">


                            <label for="title">عدد الايام :</label>
                            <input type="number" class="form-control" name="endtdateofcustomersubscriptions"
                                id="endtdateofcustomersubscriptions">





                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    </div>


                </form>


            </div>

        </div>

    </div>


    <!-- edit تعديل الاستبيان -->
    <div class="modal fade" id="exampleModal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModal2">تعديل معلومات الاستبيان</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action='../estps/update' method="post" autocomplete="off">
                    <?php echo e(method_field('patch')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">

                        <div class="form-group">
                            <label for="title">الاسم : <?php echo e($clients->name); ?></label>
                            <input type="hidden" class="form-control" name="id" id="id">
                            <input type="hidden" class="form-control" name="estps_id" id="estps_id">
                            <br>



                            <label for="title">السؤال :</label>
                            <input type="text" class="form-control" name="question" id="question"
                                readonly="readonly">
                            <label for="title">الجواب :</label>


                            <select class="form-control" name="answer" id="answer">
                                <option value="yes">نعم</option>
                                <option value="no">لا</option>
                                <option value="yesno">غير معروف</option>
                            </select>


                            <label for="title">ملاحظة :</label>
                            <textarea class="form-control" id="note" name="note" rows="3"></textarea>





                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    </div>


                </form>


            </div>

        </div>

    </div>





    <!-- edit تعديل الزيارة visit -->
    <div class="modal fade" id="exampleModal2chclientvisit" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModal2chclientvisit">تعديل الزيارة</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action='../visits/update' method="post" autocomplete="off">
                    <?php echo e(method_field('patch')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">

                        <div class="form-group">
                            <label for="title">ملاحظة الزيارة :</label>

                            <input type="hidden" class="form-control" name="id" id="id">



                            <textarea cols="63%" rows="6" id="note" name="note" required></textarea>



                            <label for="title">تاريخ الزيارة :</label>

                            <input type="date" class="form-control" name="dateofvisit" id="dateofvisit">


                            <label for="title">عدد ايام الزيارة الثانية :</label>

                            <input type="number" class="form-control" name="nextvisit" id="nextvisit">




                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">تعديل البيانات</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    </div>
                </form>
            </div>
        </div>

    </div>




    <!-- delete حذف الزيارة visit-->
    <div class="modal fade" id="modaldemo9chclientvisit" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حذف الزيارة</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>


                <form action="../visits/destroy" method="post">
                    <?php echo e(method_field('delete')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="modal-body">
                        <p>هل انت متاكد من عملية الحذف ؟</p><br>
                        <input type="hidden" name="id" id="id" value="">
                        <input class="form-control" name="note" id="note" type="text" readonly>

                        <input type="hidden" class="form-control" id="clients_id" name="clients_id"
                            value="<?php echo e($clients->id); ?>">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">الغاء</button>
                        <button type="submit" class="btn btn-danger">تاكيد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    </div>
    <!-- row closed -->
    </div>
    <!-- Container closed -->
    </div>
    <!-- main-content closed -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <!-- Internal Data tables -->
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/jszip.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/pdfmake.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/vfs_fonts.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.print.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')); ?>"></script>
    <!--Internal  Datatable js -->
    <script src="<?php echo e(URL::asset('assets/js/table-data.js')); ?>"></script>



    <script src="/p/pdfmake.js"></script>
    <script src="/p/vfs_fonts.js"></script>





    <!-- Internal Prism js-->
    <script src="<?php echo e(URL::asset('assets/plugins/prism/prism.js')); ?>"></script>
    <!--Internal  Datepicker js -->
    <script src="<?php echo e(URL::asset('assets/plugins/jquery-ui/ui/widgets/datepicker.js')); ?>"></script>
    <!-- Internal Select2 js-->
    <script src="<?php echo e(URL::asset('assets/plugins/select2/js/select2.min.js')); ?>"></script>
    <!-- Internal Modal js-->
    <script src="<?php echo e(URL::asset('assets/js/modal.js')); ?>"></script>



    <script>
        $('#exampleModal2').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var estps_id = button.data('estps_id')
            var question = button.data('question')
            var answer = button.data('answer')
            var note = button.data('note')


            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #estps_id').val(estps_id);
            modal.find('.modal-body #question').val(question);
            modal.find('.modal-body #answer').val(answer);
            modal.find('.modal-body #note').val(note);



        })
    </script>



    <script>
        $('#exampleModal2chclient').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var tahadde_id = button.data('tahadde_id')
            var startdateofcustomerchallenges = button.data('startdateofcustomerchallenges')
            var endtdateofcustomerchallenges = button.data('endtdateofcustomerchallenges')



            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #tahadde_id').val(tahadde_id);
            modal.find('.modal-body #startdateofcustomerchallenges').val(startdateofcustomerchallenges);
            modal.find('.modal-body #endtdateofcustomerchallenges').val(endtdateofcustomerchallenges);
        })
    </script>





    <script>
        $('#modaldemo8chcl').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var tahaddename = button.data('tahaddename')
            var timeenddate = button.data('timeenddate')



            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #tahaddename').val(tahaddename);
            modal.find('.modal-body #timeenddate').val(timeenddate);
        })
    </script>





    <script>
        $('#modaldemo8chclsubscriptions').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')

            var subscriptionsname = button.data('subscriptionsname')

            var startdateofcustomersubscriptions = button.data('startdateofcustomersubscriptions')
            var timeenddatesubscriptions = button.data('timeenddatesubscriptions')



            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);

            modal.find('.modal-body #subscriptionsname').val(subscriptionsname);

            modal.find('.modal-body #startdateofcustomersubscriptions').val(startdateofcustomersubscriptions);
            modal.find('.modal-body #timeenddatesubscriptions').val(timeenddatesubscriptions);
        })
    </script>



    <script>
        $("#timeenddate").keyup(function() {
            var staItems = $('#startdate').val().split("-");
            var date = new Date(Number(staItems[0]), Number(staItems[1]) - 1, Number(staItems[2]));
            date.setDate(date.getDate() + parseInt($(this).val()));
            var days = ['احد', 'اثنين', 'ثلاثاء', 'اربعاء', 'خميس', 'جمعة', 'سبت'];
            $('#fdate').val(date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear())
            $('#fdatee').val(days[date.getDay()])
            if (days[date.getDay()] == 'جمعة') {




                $('#fdatee').css({
                    'color': 'red'
                });

            } else {
                $('#fdatee').css({
                    'color': 'black'
                });

            }


        });
    </script>
    <script>
        $("#timeenddate").change(function() {
            var staItems = $('#startdate').val().split("-");
            var date = new Date(Number(staItems[0]), Number(staItems[1]) - 1, Number(staItems[2]));
            date.setDate(date.getDate() + parseInt($(this).val()));
            var days = ['احد', 'اثنين', 'ثلاثاء', 'اربعاء', 'خميس', 'جمعة', 'سبت'];
            $('#fdate').val(date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear())
            $('#fdatee').val(days[date.getDay()])
            if (days[date.getDay()] == 'جمعة') {




                $('#fdatee').css({
                    'color': 'red'
                });

            } else {
                $('#fdatee').css({
                    'color': 'black'
                });

            }


        });
    </script>








    <script>
        $("#nextvisit").change(function() {
            var staItems = $('#dateofvisit').val().split("-");
            var date = new Date(Number(staItems[0]), Number(staItems[1]) - 1, Number(staItems[2]));
            date.setDate(date.getDate() + parseInt($(this).val()));
            var days = ['احد', 'اثنين', 'ثلاثاء', 'اربعاء', 'خميس', 'جمعة', 'سبت'];
            $('#fdatevisit').val(date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear())
            $('#fdatevisitt').val(days[date.getDay()])


            if (days[date.getDay()] == 'جمعة') {




                $('#fdatevisitt').css({
                    'color': 'red'
                });

            } else {
                $('#fdatevisitt').css({
                    'color': 'black'
                });

            }


        });
    </script>


    <script>
        $("#nextvisit").keyup(function() {
            var staItems = $('#dateofvisit').val().split("-");
            var date = new Date(Number(staItems[0]), Number(staItems[1]) - 1, Number(staItems[2]));
            date.setDate(date.getDate() + parseInt($(this).val()));
            var days = ['احد', 'اثنين', 'ثلاثاء', 'اربعاء', 'خميس', 'جمعة', 'سبت'];
            $('#fdatevisit').val(date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear())
            $('#fdatevisitt').val(days[date.getDay()])
            if (days[date.getDay()] == 'جمعة') {




                $('#fdatevisitt').css({
                    'color': 'red'
                });

            } else {
                $('#fdatevisitt').css({
                    'color': 'black'
                });

            }


        });
    </script>





    <script>
        $("#timeenddatesubscriptions").change(function() {
            var staItems = $('#startdatesubscriptions').val().split("-");
            var date = new Date(Number(staItems[0]), Number(staItems[1]) - 1, Number(staItems[2]));
            date.setDate(date.getDate() + parseInt($(this).val()));
            var days = ['احد', 'اثنين', 'ثلاثاء', 'اربعاء', 'خميس', 'جمعة', 'سبت'];
            $('#fdatesubscriptions').val(date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear())
            $('#fdatesubscriptionss').val(days[date.getDay()])
            if (days[date.getDay()] == 'جمعة') {




                $('#fdatesubscriptionss').css({
                    'color': 'red'
                });

            } else {
                $('#fdatesubscriptionss').css({
                    'color': 'black'
                });

            }


        });
    </script>
    <script>
        $("#timeenddatesubscriptions").keyup(function() {
            var staItems = $('#startdatesubscriptions').val().split("-");
            var date = new Date(Number(staItems[0]), Number(staItems[1]) - 1, Number(staItems[2]));
            date.setDate(date.getDate() + parseInt($(this).val()));
            var days = ['احد', 'اثنين', 'ثلاثاء', 'اربعاء', 'خميس', 'جمعة', 'سبت'];
            $('#fdatesubscriptions').val(date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear())
            $('#fdatesubscriptionss').val(days[date.getDay()])
            if (days[date.getDay()] == 'جمعة') {




                $('#fdatesubscriptionss').css({
                    'color': 'red'
                });

            } else {
                $('#fdatesubscriptionss').css({
                    'color': 'black'
                });

            }


        });
    </script>













    <script>
        $(function() {
            $("#sarf, #kabdmoney").on("keydown keyup", sum);

            function sum() {
                $("#sum").val(Number($("#almajmoooa").val()) - Number($("#kabdmoney").val()));
            }
        });
    </script>

    <script>
        $(function() {
            $("#sarf, #kabdmoney").on("change", sum);

            function sum() {
                $("#sum").val(Number($("#almajmoooa").val()) - Number($("#kabdmoney").val()));
            }
        });
    </script>










    <script>
        $(function() {
            $("#sarfy, #kabdmoneyy").on("keydown keyup", sum);

            function sum() {
                $("#sumy").val(Number($("#almajmoooa").val()) + Number($("#sarfy").val()));
            }
        });
    </script>

    <script>
        $(function() {
            $("#sarfy, #kabdmoneyy").on("change", sum);

            function sum() {
                $("#sumy").val(Number($("#almajmoooa").val()) + Number($("#sarfy").val()));
            }
        });
    </script>








    <script type="text/javascript">
        $("button").click(function() {

            var fired_button = $("#fdate").val();
            $('#fdate').val('  ');

            var fired_button = $("#fdatee").val();
            $('#fdatee').val('  ');




            var fired_button = $("#fdatevisit").val();
            $('#fdatevisit').val('  ');

            var fired_button = $("#fdatevisitt").val();
            $('#fdatevisitt').val('  ');



            var fired_button = $("#fdatesubscriptions").val();
            $('#fdatesubscriptions').val('  ');

            var fired_button = $("#fdatesubscriptionss").val();
            $('#fdatesubscriptionss').val('  ');




        });
    </script>



    <script>
        $('#exampleModal2chsubscriptions').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var subscriptionsname = button.data('subscriptionsname')
            var startdatesubscriptions = button.data('startdatesubscriptions')
            var timeenddatesubscriptions = button.data('timeenddatesubscriptions')



            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #subscriptionsname').val(subscriptionsname);
            modal.find('.modal-body #startdatesubscriptions').val(startdatesubscriptions);
            modal.find('.modal-body #timeenddatesubscriptions').val(timeenddatesubscriptions);
        })
    </script>



    <script>
        $('#exampleModal2ch').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var tahaddename = button.data('tahaddename')
            var startdate = button.data('startdate')
            var timeenddate = button.data('timeenddate')



            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #tahaddename').val(tahaddename);
            modal.find('.modal-body #startdate').val(startdate);
            modal.find('.modal-body #timeenddate').val(timeenddate);
        })


        $('#modaldemo9chclient').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var tahaddename = button.data('tahaddename')
            var modal = $(this)

            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #tahaddename').val(tahaddename);
        })
    </script>








    <script>
        $('#exampleModal2chclientvisit').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var note = button.data('note')
            var dateofvisit = button.data('dateofvisit')
            var nextvisit = button.data('nextvisit')



            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #note').val(note);
            modal.find('.modal-body #dateofvisit').val(dateofvisit);
            modal.find('.modal-body #nextvisit').val(nextvisit);
        })


        $('#modaldemo9chclientvisit').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var note = button.data('note')
            var modal = $(this)

            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #note').val(note);
        })
    </script>





    <script>
        $('#exampleModal2chclientsubscriptions').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var subscriptions_id = button.data('subscriptions_id')
            var startdateofcustomersubscriptions = button.data('startdateofcustomersubscriptions')
            var endtdateofcustomersubscriptions = button.data('endtdateofcustomersubscriptions')



            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #subscriptions_id').val(subscriptions_id);
            modal.find('.modal-body #startdateofcustomersubscriptions').val(startdateofcustomersubscriptions);
            modal.find('.modal-body #endtdateofcustomersubscriptions').val(endtdateofcustomersubscriptions);
        })



        $('#modaldemo9chclientsubscriptions').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var subscriptions_id = button.data('subscriptions_id')
            var modal = $(this)

            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #subscriptions_id').val(subscriptions_id);
        })
    </script>























    <script>
        $('#exampleModal2chclientmoney').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var dateofmoney = button.data('dateofmoney')
            var kabdmoney = button.data('kabdmoney')
            var sarf = button.data('sarf')
            var note = button.data('note')


            id
            var modal = $(this)
            modal.find('.modal-body #id').val(id);
            modal.find('.modal-body #dateofmoney').val(dateofmoney);
            modal.find('.modal-body #kabdmoney').val(kabdmoney);
            modal.find('.modal-body #sarf').val(sarf);
            modal.find('.modal-body #note').val(note);
        })


        $('#modaldemo9chclientmoney').on('show.bs.modal', function(event) {
            var button = $(event.relatedTarget)
            var id = button.data('id')
            var modal = $(this)

            modal.find('.modal-body #id').val(id);

        })
    </script>


    <script>
        $('#example1').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>
    <script>
        $('#example2').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>

    <script>
        $('#example3').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>


    <script>
        $('#example4').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>
    <script>
        $('#example5').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>

    <script>
        $('#example6').DataTable({
            dom: 'Bfrtip',
            info: false,
            ordering: false,
            paging: false,
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>
    <script>
        $('#example76').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>

    <script>
        $('#example1999').DataTable({
            dom: 'Bfrtip',
            buttons: [
                'copy', 'excel', 'pdf'
            ]
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\nc\resources\views/estps/estpsdetalsnoe.blade.php ENDPATH**/ ?>