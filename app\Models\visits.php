<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class visits extends Model
{
    //



    use HasFactory;
    protected $guarded = [];

#########################
public function clients()
    {
        return $this->belongsTo(clients::class,'clients_id');

    }
#########################




public function historycl()
{
return $this->hasMany(historycls::class,'visit_id');

}
}









