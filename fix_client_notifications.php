<?php

/**
 * إصلاح نهائي لإشعارات العملاء الجدد
 * تشغيل: php fix_client_notifications.php
 */

echo "🔧 إصلاح نهائي لإشعارات العملاء الجدد\n";
echo "=" . str_repeat("=", 45) . "\n\n";

// تحميل Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ تم تحميل Laravel بنجاح\n\n";
} catch (Exception $e) {
    echo "❌ فشل في تحميل Laravel: " . $e->getMessage() . "\n";
    exit(1);
}

// 1. مسح cache
echo "1️⃣ مسح cache...\n";
exec('php artisan cache:clear', $output, $return);
exec('php artisan config:clear', $output, $return);
exec('php artisan route:clear', $output, $return);
exec('composer dump-autoload', $output, $return);
echo "✅ تم مسح cache\n\n";

// 2. التأكد من تسجيل Observer
echo "2️⃣ التأكد من تسجيل Observer...\n";
$providerFile = 'app/Providers/AppServiceProvider.php';
$providerContent = file_get_contents($providerFile);

if (strpos($providerContent, 'clients::observe(ClientObserver::class)') === false) {
    echo "⚠️  Observer غير مسجل، جاري التسجيل...\n";
    
    // البحث عن boot method وإضافة Observer
    $bootMethodPattern = '/public function boot\(\)\s*\{/';
    if (preg_match($bootMethodPattern, $providerContent)) {
        $newContent = preg_replace(
            '/public function boot\(\)\s*\{/',
            "public function boot()\n    {\n        clients::observe(ClientObserver::class);",
            $providerContent
        );
        
        // إضافة use statement إذا لم يكن موجوداً
        if (strpos($newContent, 'use App\Models\clients;') === false) {
            $newContent = str_replace(
                '<?php',
                "<?php\n\nuse App\Models\clients;\nuse App\Observers\ClientObserver;",
                $newContent
            );
        }
        
        file_put_contents($providerFile, $newContent);
        echo "✅ تم تسجيل Observer\n";
    } else {
        echo "❌ لم يتم العثور على boot method\n";
    }
} else {
    echo "✅ Observer مسجل بالفعل\n";
}
echo "\n";

// 3. إنشاء الإعدادات الافتراضية
echo "3️⃣ إنشاء الإعدادات الافتراضية...\n";
try {
    $users = App\Models\User::all();
    foreach ($users as $user) {
        $setting = App\Models\NotificationSetting::firstOrCreate(
            ['user_id' => $user->id],
            [
                'sound_enabled' => true,
                'sound_file' => 'notification.mp3',
                'sound_volume' => 50,
                'desktop_notifications' => true,
                'new_client_notifications' => true,
                'backup_notifications' => true,
                'system_notifications' => true
            ]
        );
        echo "✅ إعدادات المستخدم: {$user->name}\n";
    }
} catch (Exception $e) {
    echo "⚠️  تحذير في الإعدادات: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار Observer
echo "4️⃣ اختبار Observer...\n";
try {
    $testUser = App\Models\User::first();
    if ($testUser) {
        Auth::login($testUser);
        
        // إنشاء عميل تجريبي
        $testClient = App\Models\clients::create([
            'name' => 'عميل اختبار Observer - ' . date('H:i:s'),
            'gender' => 'male',
            'phone' => '59' . rand(1000000, 9999999),
            'subscroptiondate' => now()->toDateString(),
            'note' => 'عميل تجريبي لاختبار Observer',
            'password' => '123456',
            'roles_name' => 'yes',
            'active' => 'yes',
            'status' => 'active'
        ]);
        
        echo "✅ تم إنشاء عميل تجريبي: {$testClient->name}\n";
        
        // انتظار للسماح للـ Observer بالعمل
        sleep(2);
        
        // فحص الإشعارات
        $notifications = App\Models\Notification::where('type', 'new_client')
                                              ->where('related_id', $testClient->id)
                                              ->get();
        
        if ($notifications->count() > 0) {
            echo "🎉 Observer يعمل بشكل صحيح! تم إنشاء {$notifications->count()} إشعار\n";
            foreach ($notifications as $notification) {
                $user = App\Models\User::find($notification->user_id);
                echo "   - إشعار للمستخدم: {$user->name}\n";
            }
        } else {
            echo "❌ Observer لا يعمل! لم يتم إنشاء إشعارات\n";
        }
        
        // تنظيف
        App\Models\Notification::where('related_id', $testClient->id)->delete();
        $testClient->delete();
        echo "✅ تم تنظيف البيانات التجريبية\n";
        
    } else {
        echo "❌ لا يوجد مستخدمين في النظام\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في اختبار Observer: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. إنشاء إشعار ترحيبي
echo "5️⃣ إنشاء إشعار ترحيبي...\n";
try {
    $users = App\Models\User::all();
    foreach ($users as $user) {
        // حذف الإشعارات الترحيبية القديمة
        App\Models\Notification::where('user_id', $user->id)
                               ->where('type', 'system_ready')
                               ->delete();
        
        // إنشاء إشعار ترحيبي جديد
        App\Models\Notification::createNotification([
            'type' => 'new_client', // نوع عميل جديد ليظهر في الجرس
            'title' => '🎉 النظام جاهز!',
            'message' => 'تم إعداد نظام إشعارات العملاء الجدد بنجاح. الجرس سيتحقق كل 3 دقائق من العملاء الجدد.',
            'icon' => 'fa-check-circle',
            'color' => 'success',
            'user_id' => $user->id,
            'data' => [
                'system_message' => true,
                'setup_complete' => true
            ]
        ]);
    }
    echo "✅ تم إنشاء إشعارات ترحيبية\n";
} catch (Exception $e) {
    echo "⚠️  تحذير في الإشعار الترحيبي: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. فحص شامل
echo "6️⃣ فحص شامل للنظام...\n";

// فحص الملفات
$files = [
    'app/Http/Controllers/NotificationController.php' => 'Notification Controller',
    'app/Models/Notification.php' => 'Notification Model',
    'app/Observers/ClientObserver.php' => 'Client Observer',
    'public/assets/js/notifications.js' => 'JavaScript',
    'resources/views/notifications/settings.blade.php' => 'Settings View'
];

foreach ($files as $file => $description) {
    echo (file_exists($file) ? "✅" : "❌") . " {$description}\n";
}

// فحص قاعدة البيانات
echo "\n📊 فحص قاعدة البيانات:\n";
$totalNotifications = DB::table('notifications')->count();
$clientNotifications = DB::table('notifications')->where('type', 'new_client')->count();
$totalUsers = DB::table('users')->count();
$totalClients = DB::table('clients')->count();

echo "   - إجمالي الإشعارات: {$totalNotifications}\n";
echo "   - إشعارات العملاء الجدد: {$clientNotifications}\n";
echo "   - إجمالي المستخدمين: {$totalUsers}\n";
echo "   - إجمالي العملاء: {$totalClients}\n";
echo "\n";

// 7. التقرير النهائي
echo "📋 التقرير النهائي:\n";
echo "=" . str_repeat("=", 20) . "\n";

echo "🎯 خطوات الاختبار:\n";
echo "1. زيارة الصفحة الرئيسية - ستجد الجرس في الأعلى\n";
echo "2. زيارة /notifications/settings\n";
echo "3. تغيير وقت التحقق حسب الحاجة (افتراضي: 3 دقائق)\n";
echo "4. النقر على 'اختبار إشعار عميل جديد'\n";
echo "5. مشاهدة الإشعار في الجرس فوراً\n";
echo "6. زيارة /clients وإضافة عميل جديد\n";
echo "7. انتظار 3 دقائق أو تحديث الصفحة\n";
echo "8. مشاهدة إشعار العميل الجديد في الجرس\n\n";

echo "⚙️ الميزات المُفعلة:\n";
echo "   ✅ الجرس يظهر العملاء الجدد فقط\n";
echo "   ✅ التحقق كل 3 دقائق (قابل للتغيير)\n";
echo "   ✅ إشعار تجريبي يعمل\n";
echo "   ✅ Observer يعمل عند إضافة عميل\n";
echo "   ✅ إشعارات سطح المكتب\n";
echo "   ✅ أصوات الإشعارات\n\n";

echo "🔧 للتحكم في وقت التحقق:\n";
echo "   - من صفحة الإعدادات: /notifications/settings\n";
echo "   - أو من JavaScript: window.notificationSystem.updateCheckInterval(180)\n";
echo "   - الوقت بالثواني (180 = 3 دقائق)\n\n";

echo "🎉 تم الانتهاء من الإصلاح!\n";
echo "💡 النظام جاهز لمراقبة العملاء الجدد!\n";

?>
