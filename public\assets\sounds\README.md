# ملفات الأصوات للإشعارات

## الأصوات المطلوبة:

### 1. notification.mp3 (الصوت الافتراضي)
- نغمة إشعار لطيفة وهادئة
- مدة: 1-2 ثانية
- تردد متوسط

### 2. bell.mp3 (صوت جرس)
- صوت جرس كلاسيكي
- مدة: 1-3 ثواني
- نغمة واضحة

### 3. chime.mp3 (نغمة)
- نغمة موسيقية جميلة
- مدة: 2-3 ثواني
- نغمة مريحة للأذن

### 4. ding.mp3 (دينغ)
- صوت دينغ قصير وواضح
- مدة: 0.5-1 ثانية
- نغمة عالية

### 5. pop.mp3 (بوب)
- صوت بو<PERSON> خفيف
- مدة: 0.5-1 ثانية
- نغمة منخفضة

### 6. swoosh.mp3 (سووش)
- صوت سووش ناعم
- مدة: 1-2 ثانية
- تأثير صوتي ناعم

## مصادر الأصوات المجانية:

1. **Freesound.org** - مكتبة أصوات مجانية
2. **Zapsplat.com** - أصوات عالية الجودة
3. **Adobe Stock Audio** - أصوات احترافية
4. **YouTube Audio Library** - مكتبة يوتيوب الصوتية
5. **Pixabay** - أصوات مجانية

## متطلبات الملفات:

- **التنسيق**: MP3
- **الجودة**: 128kbps أو أعلى
- **الحجم**: أقل من 100KB لكل ملف
- **المدة**: 0.5-3 ثواني
- **التردد**: 44.1kHz

## كيفية إضافة الأصوات:

1. حمل الملفات الصوتية بالأسماء المحددة أعلاه
2. ضعها في مجلد `public/assets/sounds/`
3. تأكد من أن الأسماء تطابق تماماً الأسماء المطلوبة
4. اختبر الأصوات من صفحة إعدادات الإشعارات

## ملاحظات:

- تأكد من أن الأصوات ليست عالية جداً
- اختبر الأصوات على متصفحات مختلفة
- تأكد من أن الملفات غير محمية بحقوق طبع ونشر
- يمكن إضافة أصوات إضافية بتحديث ملف `NotificationSetting.php`
