<?php

namespace App\Http\Controllers;

use App\Models\BackupSetting;
use App\Models\BackupLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;

class BackupController extends Controller
{
    /**
     * عرض صفحة إدارة النسخ الاحتياطي
     */
    public function index()
    {
        // التحقق من صلاحيات المدير العام
        if (Auth::user()->role !== 'super') {
            abort(403, 'غير مخول للوصول لهذه الصفحة');
        }

        $settings = BackupSetting::getSettings();
        $backupLogs = BackupLog::orderBy('created_at', 'desc')->paginate(10);
        $availableTables = BackupSetting::getAvailableTables();

        return view('backup.index', compact('settings', 'backupLogs', 'availableTables'));
    }

    /**
     * تحديث إعدادات النسخ الاحتياطي
     */
    public function updateSettings(Request $request)
    {
        // التحقق من صلاحيات المدير العام
        if (Auth::user()->role !== 'super') {
            abort(403, 'غير مخول للوصول لهذه الصفحة');
        }

        $request->validate([
            'auto_backup_enabled' => 'boolean',
            'daily_backup' => 'boolean',
            'weekly_backup' => 'boolean',
            'monthly_backup' => 'boolean',
            'backup_path' => 'required|string',
            'backup_url' => 'nullable|url',
            'keep_backups_days' => 'required|integer|min:1',
            'compress_backups' => 'boolean',
            'backup_tables' => 'array',
            'backup_time' => 'required|string'
        ]);

        BackupSetting::updateSettings($request->all());

        session()->flash('success', 'تم تحديث إعدادات النسخ الاحتياطي بنجاح');
        return redirect()->back();
    }

    /**
     * إنشاء نسخة احتياطية يدوية
     */
    public function createManualBackup(Request $request)
    {
        // التحقق من صلاحيات المدير العام
        if (Auth::user()->role !== 'super') {
            return response()->json([
                'success' => false,
                'message' => 'غير مخول للوصول لهذه الصفحة'
            ], 403);
        }

        try {
            $backupType = $request->input('backup_type', 'manual');
            $tables = $request->input('tables', []);

            // إنشاء سجل النسخة الاحتياطية
            $backupLog = BackupLog::create([
                'backup_type' => $backupType,
                'file_name' => 'backup_' . date('Y-m-d_H-i-s') . '.sql',
                'file_path' => '',
                'status' => 'in_progress',
                'tables_backed_up' => $tables,
                'created_by' => Auth::id()
            ]);

            // تشغيل أمر النسخ الاحتياطي
            Artisan::call('backup:create', [
                '--backup-log-id' => $backupLog->id,
                '--tables' => $tables
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم بدء عملية النسخ الاحتياطي',
                'backup_id' => $backupLog->id
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحميل نسخة احتياطية
     */
    public function downloadBackup($id)
    {
        // التحقق من صلاحيات المدير العام
        if (Auth::user()->role !== 'super') {
            abort(403, 'غير مخول للوصول لهذه الصفحة');
        }

        $backupLog = BackupLog::findOrFail($id);

        if ($backupLog->status !== 'success' || !file_exists($backupLog->file_path)) {
            abort(404, 'الملف غير موجود');
        }

        return response()->download($backupLog->file_path, $backupLog->file_name);
    }

    /**
     * حذف نسخة احتياطية
     */
    public function deleteBackup($id)
    {
        // التحقق من صلاحيات المدير العام
        if (Auth::user()->role !== 'super') {
            return response()->json([
                'success' => false,
                'message' => 'غير مخول للوصول لهذه الصفحة'
            ], 403);
        }

        $backupLog = BackupLog::findOrFail($id);

        // حذف الملف من النظام
        if (file_exists($backupLog->file_path)) {
            unlink($backupLog->file_path);
        }

        // حذف السجل من قاعدة البيانات
        $backupLog->delete();

        return response()->json(['success' => true]);
    }

    /**
     * حذف النسخ القديمة
     */
    public function cleanOldBackups()
    {
        // التحقق من صلاحيات المدير العام
        if (Auth::user()->role !== 'super') {
            return response()->json([
                'success' => false,
                'message' => 'غير مخول للوصول لهذه الصفحة'
            ], 403);
        }

        $settings = BackupSetting::getSettings();
        $deletedCount = BackupLog::deleteOldBackups($settings->keep_backups_days);

        return response()->json([
            'success' => true,
            'message' => "تم حذف {$deletedCount} نسخة احتياطية قديمة"
        ]);
    }

    /**
     * الحصول على حالة النسخة الاحتياطية
     */
    public function getBackupStatus($id)
    {
        $backupLog = BackupLog::findOrFail($id);

        return response()->json([
            'status' => $backupLog->status,
            'file_size' => $backupLog->formatted_file_size,
            'duration' => $backupLog->formatted_duration,
            'error_message' => $backupLog->error_message
        ]);
    }

    /**
     * اختبار اتصال النسخ الاحتياطي الخارجي
     */
    public function testBackupConnection(Request $request)
    {
        // التحقق من صلاحيات المدير العام
        if (Auth::user()->role !== 'super') {
            return response()->json([
                'success' => false,
                'message' => 'غير مخول للوصول لهذه الصفحة'
            ], 403);
        }

        $url = $request->input('backup_url');

        if (!$url) {
            return response()->json([
                'success' => false,
                'message' => 'يرجى إدخال رابط النسخ الاحتياطي'
            ]);
        }

        try {
            // اختبار الاتصال
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_NOBODY, true);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode >= 200 && $httpCode < 400) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم الاتصال بنجاح'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في الاتصال. كود الاستجابة: ' . $httpCode
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في الاتصال: ' . $e->getMessage()
            ]);
        }
    }
}
