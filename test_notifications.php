<?php

/**
 * ملف اختبار نظام الإشعارات
 * تشغيل هذا الملف: php test_notifications.php
 */

// تحميل Laravel
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🧪 اختبار نظام الإشعارات...\n\n";

try {
    // 1. اختبار الاتصال بقاعدة البيانات
    echo "1️⃣ اختبار الاتصال بقاعدة البيانات...\n";
    $connection = DB::connection();
    $connection->getPdo();
    echo "✅ الاتصال بقاعدة البيانات ناجح\n\n";

    // 2. اختبار وجود الجداول
    echo "2️⃣ اختبار وجود الجداول...\n";
    $tables = ['notifications', 'notification_settings', 'backup_settings', 'backup_logs'];
    
    foreach ($tables as $table) {
        try {
            DB::table($table)->count();
            echo "✅ جدول $table موجود\n";
        } catch (Exception $e) {
            echo "❌ جدول $table غير موجود\n";
        }
    }
    echo "\n";

    // 3. اختبار النماذج
    echo "3️⃣ اختبار النماذج...\n";
    
    try {
        $notification = new App\Models\Notification();
        echo "✅ نموذج Notification يعمل\n";
    } catch (Exception $e) {
        echo "❌ نموذج Notification لا يعمل: " . $e->getMessage() . "\n";
    }
    
    try {
        $settings = new App\Models\NotificationSetting();
        echo "✅ نموذج NotificationSetting يعمل\n";
    } catch (Exception $e) {
        echo "❌ نموذج NotificationSetting لا يعمل: " . $e->getMessage() . "\n";
    }
    
    try {
        $backupSetting = new App\Models\BackupSetting();
        echo "✅ نموذج BackupSetting يعمل\n";
    } catch (Exception $e) {
        echo "❌ نموذج BackupSetting لا يعمل: " . $e->getMessage() . "\n";
    }
    
    try {
        $backupLog = new App\Models\BackupLog();
        echo "✅ نموذج BackupLog يعمل\n";
    } catch (Exception $e) {
        echo "❌ نموذج BackupLog لا يعمل: " . $e->getMessage() . "\n";
    }
    echo "\n";

    // 4. اختبار إنشاء إعدادات افتراضية
    echo "4️⃣ اختبار إنشاء الإعدادات الافتراضية...\n";
    
    try {
        $backupSettings = App\Models\BackupSetting::getSettings();
        echo "✅ إعدادات النسخ الاحتياطي تم إنشاؤها\n";
    } catch (Exception $e) {
        echo "❌ فشل في إنشاء إعدادات النسخ الاحتياطي: " . $e->getMessage() . "\n";
    }
    
    // 5. اختبار إنشاء إشعار تجريبي
    echo "\n5️⃣ اختبار إنشاء إشعار تجريبي...\n";
    
    try {
        // البحث عن أول مستخدم
        $user = App\Models\User::first();
        
        if ($user) {
            $notification = App\Models\Notification::createNotification([
                'type' => 'test',
                'title' => 'اختبار النظام',
                'message' => 'تم اختبار نظام الإشعارات بنجاح!',
                'icon' => 'fa-check-circle',
                'color' => 'success',
                'user_id' => $user->id
            ]);
            
            echo "✅ تم إنشاء إشعار تجريبي بنجاح (ID: {$notification->id})\n";
        } else {
            echo "⚠️  لا يوجد مستخدمين في النظام\n";
        }
    } catch (Exception $e) {
        echo "❌ فشل في إنشاء إشعار تجريبي: " . $e->getMessage() . "\n";
    }

    echo "\n🎉 انتهى الاختبار!\n";
    echo "💡 يمكنك الآن زيارة /notifications لرؤية الإشعارات\n";

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "📋 تحقق من:\n";
    echo "   - إعدادات قاعدة البيانات في .env\n";
    echo "   - تشغيل php artisan migrate\n";
    echo "   - صلاحيات المجلدات\n";
}

?>
