<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;


class infocompanies extends Model
{

    use HasFactory;
    protected $guarded = [];


    /**
     * The attributes that are mass assignable.
     *
     *
     */





}
