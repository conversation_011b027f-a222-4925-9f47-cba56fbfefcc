<!-- قائمة العملاء الجدد -->
<div class="dropdown" id="newClientsDropdown">
    <button class="btn btn-outline-success dropdown-toggle position-relative" type="button" id="newClientsButton" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fa fa-users me-2"></i>
        العملاء الجدد
        <span class="badge bg-success position-absolute top-0 start-100 translate-middle rounded-pill" id="newClientsCount" style="display: none;">
            0
        </span>
    </button>
    
    <div class="dropdown-menu dropdown-menu-end shadow-lg" id="newClientsMenu" style="width: 400px; max-height: 500px; overflow-y: auto;">
        <!-- Header -->
        <div class="dropdown-header bg-success text-white p-3 mb-0">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fa fa-user-plus me-2"></i>
                    العملاء الجدد
                </h6>
                <div>
                    <button class="btn btn-sm btn-outline-light me-1" id="refreshNewClients" title="تحديث">
                        <i class="fa fa-sync"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-light" id="viewAllClients" title="عرض جميع العملاء">
                        <i class="fa fa-list"></i>
                    </button>
                </div>
            </div>
            <small class="text-light opacity-75" id="lastUpdateTime">آخر تحديث: الآن</small>
        </div>

        <!-- Loading -->
        <div class="text-center p-4" id="clientsLoading">
            <div class="spinner-border spinner-border-sm text-success me-2" role="status"></div>
            جاري التحميل...
        </div>

        <!-- Empty State -->
        <div class="text-center p-4" id="noNewClients" style="display: none;">
            <i class="fa fa-users text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
            <h6 class="text-muted mt-3">لا يوجد عملاء جدد</h6>
            <small class="text-muted">ستظهر العملاء الجدد هنا</small>
        </div>

        <!-- Clients List -->
        <div id="newClientsList"></div>

        <!-- Footer -->
        <div class="dropdown-divider"></div>
        <div class="dropdown-item-text text-center">
            <button class="btn btn-success btn-sm" onclick="window.location.href='/clients'">
                <i class="fa fa-plus me-1"></i>
                إضافة عميل جديد
            </button>
        </div>
    </div>
</div>

<style>
/* تنسيق قائمة العملاء الجدد */
.new-client-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.new-client-item:hover {
    background-color: #f8f9fa;
    transform: translateX(-2px);
}

.new-client-item:last-child {
    border-bottom: none;
}

.client-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    margin-left: 12px;
    flex-shrink: 0;
}

.client-info {
    flex: 1;
    min-width: 0;
}

.client-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    font-size: 14px;
}

.client-details {
    font-size: 12px;
    color: #7f8c8d;
    margin-bottom: 4px;
}

.client-time {
    font-size: 11px;
    color: #bdc3c7;
    display: flex;
    align-items: center;
}

.client-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.new-client-item:hover .client-actions {
    opacity: 1;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.new-badge {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: 8px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 576px) {
    #newClientsMenu {
        width: 100vw !important;
        right: 0 !important;
        left: 0 !important;
        margin: 0;
        border-radius: 0;
        max-height: 70vh;
    }
}
</style>

<script>
class NewClientsManager {
    constructor() {
        this.clients = [];
        this.lastUpdate = null;
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.loadNewClients();
        this.setupEventListeners();
        this.startAutoUpdate();
    }

    setupEventListeners() {
        // تحديث يدوي
        document.getElementById('refreshNewClients')?.addEventListener('click', () => {
            this.loadNewClients();
        });

        // عرض جميع العملاء
        document.getElementById('viewAllClients')?.addEventListener('click', () => {
            window.location.href = '/clients';
        });

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!document.getElementById('newClientsDropdown').contains(e.target)) {
                const dropdown = bootstrap.Dropdown.getInstance(document.getElementById('newClientsButton'));
                if (dropdown) {
                    dropdown.hide();
                }
            }
        });
    }

    async loadNewClients() {
        try {
            this.showLoading(true);
            
            const response = await fetch('/api/new-clients');
            if (response.ok) {
                const data = await response.json();
                this.clients = data.clients || [];
                this.updateUI();
                this.updateLastUpdateTime();
            } else {
                throw new Error('فشل في تحميل العملاء الجدد');
            }
        } catch (error) {
            console.error('خطأ في تحميل العملاء الجدد:', error);
            this.showError('حدث خطأ في تحميل العملاء الجدد');
        } finally {
            this.showLoading(false);
        }
    }

    updateUI() {
        const countBadge = document.getElementById('newClientsCount');
        const clientsList = document.getElementById('newClientsList');
        const noClientsDiv = document.getElementById('noNewClients');

        // تحديث العداد
        if (this.clients.length > 0) {
            countBadge.textContent = this.clients.length;
            countBadge.style.display = 'block';
            countBadge.classList.add('pulse-animation');
        } else {
            countBadge.style.display = 'none';
            countBadge.classList.remove('pulse-animation');
        }

        // تحديث القائمة
        if (this.clients.length === 0) {
            clientsList.innerHTML = '';
            noClientsDiv.style.display = 'block';
        } else {
            noClientsDiv.style.display = 'none';
            clientsList.innerHTML = this.clients.map(client => this.renderClientItem(client)).join('');
        }
    }

    renderClientItem(client) {
        const avatarColor = this.getAvatarColor(client.name);
        const initials = this.getInitials(client.name);
        const timeAgo = this.getTimeAgo(client.created_at);
        
        return `
            <div class="new-client-item" onclick="this.viewClient(${client.id})">
                <div class="d-flex align-items-center">
                    <div class="client-avatar" style="background-color: ${avatarColor};">
                        ${initials}
                    </div>
                    <div class="client-info">
                        <div class="client-name">
                            ${client.name}
                            <span class="new-badge">جديد</span>
                        </div>
                        <div class="client-details">
                            <i class="fa fa-phone me-1"></i>
                            ${client.phone || 'لا يوجد رقم'}
                            ${client.gender ? `• ${client.gender === 'male' ? 'ذكر' : 'أنثى'}` : ''}
                        </div>
                        <div class="client-time">
                            <i class="fa fa-clock me-1"></i>
                            ${timeAgo}
                        </div>
                    </div>
                    <div class="client-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); this.viewClient(${client.id})" title="عرض">
                            <i class="fa fa-eye"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    getAvatarColor(name) {
        const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e'];
        const index = name.charCodeAt(0) % colors.length;
        return colors[index];
    }

    getInitials(name) {
        return name.split(' ').map(word => word.charAt(0)).join('').substring(0, 2).toUpperCase();
    }

    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        return `منذ ${diffInDays} يوم`;
    }

    viewClient(clientId) {
        window.location.href = `/clients/${clientId}`;
    }

    showLoading(show) {
        const loadingDiv = document.getElementById('clientsLoading');
        loadingDiv.style.display = show ? 'block' : 'none';
    }

    showError(message) {
        const clientsList = document.getElementById('newClientsList');
        clientsList.innerHTML = `
            <div class="text-center p-4 text-danger">
                <i class="fa fa-exclamation-triangle mb-2"></i><br>
                ${message}
            </div>
        `;
    }

    updateLastUpdateTime() {
        const timeElement = document.getElementById('lastUpdateTime');
        timeElement.textContent = `آخر تحديث: ${new Date().toLocaleTimeString('ar-SA')}`;
    }

    startAutoUpdate() {
        // تحديث كل 3 دقائق
        this.updateInterval = setInterval(() => {
            this.loadNewClients();
        }, 180000);
    }

    // دالة لإضافة عميل جديد فوراً (تستدعى من Observer)
    addNewClient(clientData) {
        this.clients.unshift(clientData);
        this.updateUI();
        
        // إشعار صوتي
        this.playNotificationSound();
        
        // إشعار سطح المكتب
        this.showDesktopNotification(clientData);
    }

    playNotificationSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = 800;
            oscillator.type = "sine";
            
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        } catch (error) {
            console.warn('فشل في تشغيل الصوت:', error);
        }
    }

    showDesktopNotification(client) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('عميل جديد!', {
                body: `تم إضافة العميل: ${client.name}${client.phone ? ' - ' + client.phone : ''}`,
                icon: '/favicon.ico',
                tag: 'new-client'
            });
        }
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.newClientsManager = new NewClientsManager();
    
    // طلب إذن الإشعارات
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
});
</script>
